<div>
  <div
    *ngFor="let notification of notifications; let i = index"
    class="col px-0 d-flex pb-2"
  >
    <div class="card flex-fill border-0 shadow1">
      <div class="card-body p-2">
        <div class="d-flex flex-row justify-content-between">
          <!-- Avatar or Initials -->
          <div class="d-flex align-items-center">
            <div *ngIf="notification.avatar; else initials" class="mr-2">
              <img
                [src]="notification.avatar"
                alt="Avatar"
                class="rounded-circle"
                width="48px"
                height="48px"
              />
            </div>
            <ng-template #initials>
              <div class="initials-circle mr-2">
                {{ getInitials(notification.sender) }}
              </div>
            </ng-template>
            <div>
              <h6
                class="mb-0"
                [ngClass]="{
                  '': !notification.isRead,
                  'text-inactive': notification.isRead
                }"
              >
                {{ notification.message }}
              </h6>
              <span class="small text-muted">{{ notification.date }}</span>
            </div>
          </div>

          <!-- Actions (Read/Unread & Unpin) -->
          <div
            class="small cursor-pointer"
            [ngClass]="{
              'text-primary': !notification.isRead,
              'text-inactive': notification.isRead
            }"
            (click)="notification.isRead ? markAsUnread(i) : markAsRead(i)"
          >
            {{ notification.isRead ? "Mark as Unread" : "Mark as Read" }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
