import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ThemeSwitchService {
  private theme: 'light' | 'dark' = 'light';

  constructor() {
    this.loadTheme();
  }

  toggleTheme(): void {
    this.theme = this.theme === 'light' ? 'dark' : 'light';
    this.applyTheme(this.theme);
  }

  applyTheme(theme: 'light' | 'dark'): void {
    this.theme = theme;
    document.body.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
  }

  loadTheme(): void {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      this.applyTheme(savedTheme as 'light' | 'dark');
    } else {
      this.applySystemTheme();
    }
  }

  applySystemTheme(): void {
    const prefersDark = window.matchMedia(
      '(prefers-color-scheme: dark)'
    ).matches;
    const theme = prefersDark ? 'dark' : 'light';
    this.applyTheme(theme);
  }

  getCurrentTheme(): 'light' | 'dark' {
    return this.theme;
  }
}
