<div *ngIf="!showActiveSupport">
  <!-- <div>
    <div>
      <div class="col px-0 d-flex pb-2" (click)="onViewMyTickets()">
        <div class="card flex-fill border-0 shadow1">
          <div class="card-body p-2 cursor-pointer">
            <div
              class="d-flex flex-row justify-content-between align-items-center"
            >
              <div class="col px-2 mb-0 py-2 body2 ellipsis text-nowrap">
                <h5 class="mb-0">Submit Feedback</h5>
              </div>
              <div
                class="col-auto px-1 d-flex flex-row table-hover cursor-pointer"
              >
                <img src="{{ assetBasePath }}Icon/chevron_left.svg" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div> -->

  <div class="mb-3">
    <h6>FAQs</h6>
    <hr />
    <div *ngFor="let faq of faqs">
      <div class="col px-0 d-flex pb-2" (click)="onSelectFaq(faq)">
        <div class="card flex-fill border-0 shadow1">
          <div class="card-body p-2">
            <div
              class="d-flex flex-row justify-content-between align-items-center"
            >
              <div class="col px-2 mb-0 body2 py-2">
                <h6 class="mb-0">{{ faq }}</h6>
              </div>
              <div
                class="col-auto px-1 d-flex flex-row table-hover cursor-pointer"
              >
                <img src="{{ assetBasePath }}Icon/chevron_left.svg" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="mb-3">
    <h6>Didn't the FAQs resolve your query?</h6>
    <hr />
    <div>
      <div class="col-auto px-0 d-flex pb-2">
        <button
          class="btn btn-dark-blue d-flex flex-nowrap text-white"
          (click)="onViewMyTickets()"
        >
          Submit Feedback
        </button>
      </div>
    </div>
  </div>
</div>

<div *ngIf="showActiveSupport">
  <app-active-support
    [selectedFaq]="selectedFaq"
    (feedbackGiven)="onFeedbackGiven($event)"
  >
  </app-active-support>
</div>
