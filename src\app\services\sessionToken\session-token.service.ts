import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class SessionTokenService {
  private tokenKey: string = 'sessionToken'; // Key to store the session token in localStorage
  private cookieName: string = 'ASP.NET_SessionId_Shared'; // Name of the cookie containing the session token

  constructor() {}

  /**
   * Checks for changes in the session token. If a change is detected, localStorage is cleared.
   * @returns {boolean} - True if token changed, false otherwise.
   */
  checkForTokenChange(): boolean {
    const currentToken = this.getSessionTokenFromCookie();
    console.log('Current Token from Cookie:', currentToken);

    const storedToken = localStorage.getItem(this.tokenKey);
    console.log('Stored Token:', storedToken);

    if (storedToken !== currentToken) {
      console.log('Token changed. Clearing localStorage.');
      this.clearLocalStorage();
      localStorage.setItem(this.tokenKey, currentToken || '');
      return true; // Token changed
    }

    console.log('No change in token.');
    return false; // No change in token
  }

  /**
   * Retrieves the session token from cookies.
   * @returns {string | null} - The session token if found, otherwise null.
   */
  private getSessionTokenFromCookie(): string | null {
    console.log('Document Cookies:', document.cookie);
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [key, value] = cookie.split('=').map((c) => c.trim());
      if (key === this.cookieName) {
        console.log('Found Session Token:', value);
        return value || null;
      }
    }
    console.log('Session Token not found in cookies.');
    return null;
  }

  /**
   * Clears the local storage.
   */
  private clearLocalStorage(): void {
    localStorage.clear();
    console.log('Local storage cleared due to session token change.');
  }
}
