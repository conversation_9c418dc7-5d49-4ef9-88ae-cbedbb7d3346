import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { AlertService } from 'src/app/services/alert/alert.service';
import { ChatService } from 'src/app/services/chat/chat.service';
import { LogService } from 'src/app/services/log/log.service';
import { PageSelectionService } from 'src/app/services/page-select/page-selection.service';
import { environment } from 'src/environments/environment.development';

// Define the Person interface
interface Person {
  id: number;
  name: string;
  email: string;
  avatar?: string; // Optional field
  teamIds?: number[]; // Optional array of team IDs
}

// Define the Team interface
interface Team {
  id: number;
  name: string;
}

interface Prompt {
  id: number;
  title: string;
  desc: string;
  type: string; // Type of prompt (e.g., "ORGANIZATION", "MY PROMPT", etc.)
  sharedWith?: { avatar?: string; name: string }[]; // People with access
  userId: string;
}

@Component({
  selector: 'app-prompt-vault',
  templateUrl: './prompt-vault.component.html',
  styleUrls: ['./prompt-vault.component.css'],
})
export class PromptVaultComponent {
  isLoading: boolean = false;

  successMessage: string = '';
  shareCompleted: boolean = false;
  externalText = '';

  promptLimitsAPI: { [key: string]: number } = {};

  totalTypeLengthAPI: number = 0;

  // Pagination state
  currentSkip = 0;
  currentLimit = 2;
  totalPrompts = 0;
  promptsData: Prompt[] = []; // Store fetched prompts
  activePromptType: string | null = null;

  promptId: number | null = null;
  url: string = environment.apiUrl;

  private baseUrl = `${this.url}/prompts`;

  promptLengths: Record<string, number> = {};

  promptLengthsAPI: Record<string, number> = {};

  viewAllState: Record<string, boolean> = {
    global_alias: false,
    personal: false,
    shared: false,
  };

  globalPromptTotal: number = 0;
  personalPromptTotal: number = 0;
  sharedPromptTotal: number = 0;

  editedDec: string = '';
  inputDec: string = '';

  newPromptTitle: string = '';
  newPromptDescription: string = '';
  user_ID: number = 1;
  isDecView: boolean = false;
  assetBasePath: string = environment.assetBasePath;

  constructor(
    private pageSelectionService: PageSelectionService,
    private chatService: ChatService,
    private alertService: AlertService,
    private http: HttpClient,
    private logService: LogService
  ) {}

  selectedOptions: {
    team: string;
    name: string;
    email: string;
    avatar?: string;
    id: number;
    teamIds?: number[];
  }[] = [];
  options: {
    team: string;
    name: string;
    email: string;
    avatar?: string;
    id: number;
    teamIds?: number[];
  }[] = [];

  people: Person[] = [
    {
      id: 1,
      name: 'Alice Johnson',
      email: '<EMAIL>',
      avatar: '',
      teamIds: [1],
    },
    { id: 2, name: 'Bob Smith', email: '<EMAIL>', teamIds: [1, 2, 3] },
    {
      id: 3,
      name: 'Carol Williams',
      email: '<EMAIL>',
      avatar: '../../../assets/avatar.png',
      teamIds: [],
    },
    { id: 4, name: 'Dave Brown', email: '<EMAIL>' },
  ];

  teams: Team[] = [
    { id: 1, name: 'Engineering' },
    { id: 2, name: 'Marketing' },
    { id: 3, name: 'Product' },
  ];

  promptss: any[] = [];

  ngOnInit() {
    this.populateOptions();
    this.loadPrompts('personal'); // Initially load personal prompts
    this.loadPrompts('global_alias');
    this.loadPrompts('shared');
  }

  loadPrompts(promptType?: string): void {
    const apiUrl = `${this.baseUrl}/?skip=${this.currentSkip}&limit=${this.currentLimit}&prompt_type=${promptType}`;
    this.promptss = this.promptss.filter(
      (prompt) => prompt.type !== promptType
    );
    this.http.get<any[]>(apiUrl, { withCredentials: true }).subscribe(
      (response) => {
        if (Array.isArray(response)) {
          // Map API response to match the Prompt interface
          const mappedPrompts = response.map((item) => ({
            id: item.id,
            title: item.content, // Map content to title
            desc: item.description || 'No description available', // Handle null descriptions
            type: item.prompt_type, // Map prompt_type to type
            timeAgo: new Date(item.updated_at).toLocaleString(), // Format updated_at
            sharedWith: [], // Add sharedWith field if needed
          }));
          const filteredPrompts = mappedPrompts.filter(
            (prompt) => prompt.type === promptType
          );

          this.promptss = this.promptss.concat(filteredPrompts);
          this.totalPrompts = this.promptss.length; // Calculate total prompts from the array length
          // Store the count for each prompt type
          if (promptType === 'shared') {
            this.sharedPromptTotal = filteredPrompts.length;
          } else if (promptType === 'personal') {
            this.personalPromptTotal = filteredPrompts.length;
          } else if (promptType === 'global_alias') {
            this.globalPromptTotal = filteredPrompts.length;
          }

          this.isLoading = false;
          this.logService.log('Mapped Prompts:' + promptType + this.promptss);
          this.logService.log('Total Prompts:' + this.totalPrompts);
          this.logService.log('Global Prompt Total:' + this.globalPromptTotal);
          this.logService.log(
            'Personal Prompt Total:' + this.personalPromptTotal
          );
          this.logService.log('Shared Prompt Total:' + this.sharedPromptTotal); // Debug total count
        } else {
          this.logService.error('Unexpected API response format:' + response);
          this.promptss = [];
          this.totalPrompts = 0;
        }
      },
      (error) => {
        this.logService.error('Error fetching prompts:' + error);
        this.alertService.showAlert(
          'Failed to load prompts. Please try again later.',
          'error'
        );
      }
    );
  }

  populateOptions() {
    const teamMap = this.teams.reduce((acc, team) => {
      acc[team.id] = team.name;
      return acc;
    }, {} as { [key: number]: string });

    this.people.forEach((person) => {
      if (person.teamIds && person.teamIds.length > 0) {
        person.teamIds.forEach((teamId) => {
          this.options.push({
            team: teamMap[teamId] || 'Unknown Team',
            name: person.name,
            email: person.email,
            avatar: person.avatar || '',
            id: person.id, // Store ID
            teamIds: person.teamIds, // Include teamIds
          });
        });
      } else {
        this.options.push({
          team: 'No Team',
          name: person.name,
          email: person.email,
          avatar: person.avatar || '',
          id: person.id, // Store ID
          teamIds: [], // Default teamIds
        });
      }
    });
  }

  getUniqueTeams(): string[] {
    const uniqueTeams = new Set<string>();

    // Check each team for membership
    this.teams.forEach((team) => {
      const teamMembers = this.people.filter((person) =>
        person.teamIds?.includes(team.id)
      );

      // Get the IDs of the selected options
      const selectedIds = new Set(
        this.selectedOptions.map((option) => option.id)
      );

      // Check if all team members are selected
      const allMembersSelected = teamMembers.every((member) =>
        selectedIds.has(member.id)
      );

      // If all members of the team are selected, add the team to the unique set
      if (allMembersSelected) {
        uniqueTeams.add(team.name);
      }
    });

    return Array.from(uniqueTeams);
  }

  sharePrompts() {
    this.shareCompleted = true;
    this.successMessage = 'Prompts shared successfully!';
    setTimeout(() => {
      this.successMessage = '';
    }, 3000);
  }

  toChatPage(page: string) {
    this.pageSelectionService.setSelectedPage(page);
  }

  executePrompt(prompt: string) {
    this.chatService.saveExternalInput(prompt);
  }

  clearExternalInput() {
    this.externalText = ''; // Clear the external input
    this.chatService.clearExternalInput(); // Clear from ChatService
    this.chatService.clearChatState(); // Reset the chat state
    // Optionally, reset any other state or UI related to the chat
  }

  togglePromptLimit(type: string) {
    this.activePromptType = type;

    if (this.viewAllState[type]) {
      // Fetch all prompts for the selected type
      this.currentLimit = 2;
      this.currentSkip = 0;
      this.viewAllState[type] = false;
    } else {
      // Reset to default limit
      this.currentLimit = 10;
      this.currentSkip = 0;
      this.viewAllState[type] = true;
    }
    this.loadPrompts(type);
  }

  // Handle pagination
  nextPage() {
    if (this.currentSkip + this.currentLimit < this.totalPrompts) {
      this.currentSkip += this.currentLimit;
      this.loadPrompts(this.activePromptType!);
    }
  }

  previousPage() {
    if (this.currentSkip >= this.currentLimit) {
      this.currentSkip -= this.currentLimit;
      this.loadPrompts(this.activePromptType!);
    }
  }

  selectedPromptType(type: string): { isViewAll: boolean; activeType: string } {
    const isViewAll = this.viewAllState[type];
    return { isViewAll, activeType: type };
  }

  calculatePromptLengths(): void {
    // Calculate the lengths based on the current filtered list of prompts
    this.promptLengths = this.promptss.reduce((acc, prompt) => {
      acc[prompt.prompt_type] = (acc[prompt.prompt_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // For debugging purposes
    this.logService.log('' + this.promptLengths);
  }
  // Method to handle Add button click with validation
  onAddPrompt(
    user_ID: number,
    prompt_type: string,
    title: string,
    description: string
  ): void {
    // Check if the inputs are not empty
    if (!title.trim()) {
      this.alertService.showAlert('Your prompt text is missing', 'info');

      return;
    }

    // Proceed with adding the prompt if inputs are valid
    console.log('clicked', this.baseUrl);
    this.AddPrompt(user_ID, prompt_type, title, description);
  }
  // Method to handle adding a new prompt
  AddPrompt(
    user_ID: number,
    prompt_type: string,
    title: string,
    description: string
  ): void {
    // Construct the new prompt data
    const newPrompt = {
      userId: user_ID, // Assuming the prompt is created by a user with this ID
      prompt_type: prompt_type,
      content: title,
      description: description,
    };

    // Send the new prompt to the server
    this.http
      .post<any>(`${this.baseUrl}/?attempt=0`, newPrompt, {
        withCredentials: true,
      })
      .subscribe(
        (response) => {
          // Assuming the response contains the new prompt with ID and other details
          const addedPrompt: Prompt = {
            id: response.id,
            title: response.content, // Map content to title
            desc: response.description || 'No description available', // Handle null descriptions
            type: response.prompt_type, // Map prompt_type to type
            sharedWith: [], // Add sharedWith field if needed
            userId: response.created_by,
          };

          // Add the newly created prompt to the list of prompts
          this.promptss.unshift(addedPrompt);
          this.totalPrompts += 1; // Update the total prompts count

          // Notify the user about the success
          this.alertService.showAlert('Prompt added successfully!', 'success');
        },
        (error) => {
          this.logService.error('Error adding prompt:' + error);
          this.alertService.showAlert(
            'Failed to add prompt. Please try again later.',
            'error'
          );
        }
      );
  }

  // Method to clear the input fields
  ClearPromptsInputs(): void {
    this.newPromptTitle = '';
    this.newPromptDescription = '';
  }

  startEdit(promptId: number): void {
    this.logService.log('Editing prompt with id:' + promptId);
    const promptToEdit = this.promptss.find((prompt) => prompt.id === promptId);

    if (promptToEdit) {
      this.editedDec = promptToEdit.desc || ''; // Load the current description for viewing
      this.inputDec = '';
      this.promptId = promptToEdit.id; // Store the promptId being edited
    } else {
      this.logService.error('Prompt with id' + promptId + 'not found!');
    }
  }

  isReadVew(isRead: boolean) {
    if (isRead === true) {
      this.isDecView = true;
    } else this.isDecView = false;
    return this.isDecView, this.logService.log('' + this.isDecView);
  }

  saveEdit(): void {
    if (this.editedDec.trim() && this.promptId !== null) {
      this.logService.log('Saving updated Dec for promptId:' + this.promptId);
      this.http
        .put(
          `${this.baseUrl}/${this.promptId}`,
          {
            description: this.inputDec,
          },
          { withCredentials: true }
        )
        .subscribe(
          (response) => {
            // Update the description in the local array
            const promptToUpdate = this.promptss.find(
              (prompt) => prompt.id === this.promptId
            );
            if (promptToUpdate) {
              promptToUpdate.desc = this.inputDec; // Update local data
            }

            this.inputDec = ''; // Clear the input after saving
            this.alertService.showAlert(
              'Description updated successfully!',
              'success'
            );
            this.loadPrompts('personal'); // Reload prompts to reflect the change
          },
          (error) => {
            this.logService.error('Error updating description:' + error);
            this.alertService.showAlert(
              'Failed to update description. Please try again later.',
              'error'
            );
          }
        );
    }
  }

  deletePromptAPI(itemId: string): void {
    this.logService.log(itemId);

    this.http
      .delete(`${this.baseUrl}/${itemId}`, { withCredentials: true })
      .subscribe({
        next: () => {
          this.logService.log(`Item with ID ${itemId} deleted successfully.`);
          // Optionally, refresh the list
          this.alertService.showAlert(
            'Prompt Deleted successfully!',
            'success'
          );
          this.loadPrompts('personal');
          this.loadPrompts('global_alias');
          this.loadPrompts('shared');
        },
        error: (err: any) => {
          this.logService.error(`Error deleting item with ID ${itemId}:` + err);
        },
      });
  }
}
