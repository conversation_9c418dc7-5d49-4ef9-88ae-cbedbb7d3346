import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class DateService {
  constructor() {}

  formatDate(dateString: string): string {
    const entryDate = new Date(dateString); // Entry date in UTC
    const now = new Date(); // Current local time

    // Convert entry date to local time
    const localEntryDate = new Date(
      entryDate.getUTCFullYear(),
      entryDate.getUTCMonth(),
      entryDate.getUTCDate(),
      entryDate.getUTCHours(),
      entryDate.getUTCMinutes(),
      entryDate.getUTCSeconds()
    );

    // Local now time adjusted to the same UTC start of the day
    const localNow = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const localYesterday = new Date(localNow);
    localYesterday.setDate(localNow.getDate() - 1);

    const oneWeekAgo = new Date(localNow);
    oneWeekAgo.setDate(localNow.getDate() - 7);

    if (localEntryDate >= localNow) {
      return 'Today';
    } else if (localEntryDate >= localYesterday) {
      return 'Yesterday';
    } else if (localEntryDate >= oneWeekAgo) {
      // Return day of the week for entries within the last week
      return localEntryDate.toLocaleDateString('en-GB', { weekday: 'long' });
    } else {
      // Return formatted date for older entries
      return localEntryDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
      });
    }
  }
}
