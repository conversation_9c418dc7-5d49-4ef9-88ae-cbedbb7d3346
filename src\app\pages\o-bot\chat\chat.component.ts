import {
  Component,
  OnInit,
  ElementRef,
  ViewChild,
  ChangeDetectorRef,
  Input,
  EventEmitter,
  Output,
} from '@angular/core';
import { ChatService } from 'src/app/services/chat/chat.service'; // Adjust the import path as necessary
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert/alert.service';
import * as XLSX from 'xlsx';
import { PageSelectionService } from 'src/app/services/page-select/page-selection.service';
import { Subscription } from 'rxjs';
import { environment } from 'src/environments/environment.development';
import { LogService } from 'src/app/services/log/log.service';
import { UserInfoService } from 'src/app/services/user-info/user-info.service';
import { parseAquaProResponse } from 'src/app/utils/aquapro-utils';

// Import jQuery for modal operations
declare var $: any;

interface TableRow {
  [key: string]: string | boolean | undefined; // Flexibly allow various types
  visible?: boolean; // Optional property for visibility
}

interface BotResponse {
  text: string;
  buttons?: { label: string; action: string }[];
  tableData?: TableRow[]; // Array of rows with varying columns
}

interface Message {
  convID?: any;
  text: string;
  sender: 'user' | 'bot';
  title?: string;
  thread?: [];
  messageID?: any;
  extraData?: ExtraData;
  buttons?: { label: string; action: string }[];
  tableData?: TableRow[]; // Use the updated TableRow type
  buttonsVisible?: boolean;
  tableVisible?: boolean;
  excelData?: any[][]; // Add this field for individual Excel data
  liked?: boolean;
  disliked?: boolean;
  fileName?: string | null;
}

interface ExtraData {
  message_id: any;
  filename: any;
  content: any;
}
@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.css'],
})
export class ChatComponent implements OnInit {
  @ViewChild('chatContainer') chatContainer!: ElementRef;

  @Input() fullScreenToggle?: boolean;
  @Input() externalInput: string = '';
  @Input() buttonNewChat?: boolean;

  private pageSelectionSubscription: Subscription | null = null;

  userInput: string = '';
  isTyping: boolean = false;
  isLoading: boolean = false;

  isChatActive: boolean = false;
  currentResponseIndex: number = 0;
  isHovered = false;
  previousExternalInput: string = '';
  externalInputProcessed: boolean = false;

  convData: any = null;
  chatTitle: string = '';

  selectedPage: any = '';

  excelData: any[][] = [];
  fileName: string = 'exported-file.xlsx';
  base64Encoded: string = '';

  threadId: string = '123';
  attachedFileName: string | null = null;
  attachedFile: File | null = null;
  attachedFileData: string | null = '';

  url: string = environment.apiUrl;
  isPinned: boolean = false;

  assetBasePath: string = environment.assetBasePath;

  allSuggestions: string[] = [
    'Create a P-FMEA for Copper Plated Sparkplug, in Engine Alpha',
    'Identify Relevant Documents to Generate Control Plan for Copper-Plated Spark Plug',
    'Generate Potential Causes of Failure for Copper-Plated Sparkplug, P-FMEA',
    'Generate Potential Failure modes for Operation 30, Copper-Plated Spark Plug, PFMEA',
    'Analyze Failure Mode for Spark Plug in Engine Beta',
    'Review Control Plan for Copper-Plated Spark Plug',
    'Identify Risks Associated with Copper-Plated Sparkplug',
    'Create a Risk Mitigation Strategy for Engine Components',
  ];

  suggestions: string[] = [
    'Generate Design FMEA (DFMEA)',
    'Generate Process Flow',
    'Generate Process FMEA (PFMEA)',
    'Generate Control Plan',
  ];

  messages: Message[] = [];
  hoverStates: { [key: number]: boolean } = {};

  userName: string = '';
  isrefreshing: boolean = true;

  constructor(
    private chatService: ChatService,
    private cdr: ChangeDetectorRef,
    private http: HttpClient,
    private alertService: AlertService,
    private pageSelectionService: PageSelectionService,
    private logService: LogService,
    private userService: UserInfoService
  ) {
    this.chatService.chatTitle$.subscribe((title) => {
      this.chatTitle = title;
    });
  }

  private baseUrl = `${this.url}/message`; // Base URL for the API
  private pinUrl = `${this.url}/chat`;

  ngOnInit() {
    this.fetchUserInfo();
    this.logService.log('ChatComponent initialized');
    this.loadState();
    this.checkForExternalInput();
    // Subscribe to page changes and reload state accordingly
    this.pageSelectionSubscription =
      this.pageSelectionService.selectedPage$.subscribe((page) => {
        if (page === 'New Chat') {
          this.logService.log('Page changed to:' + page);
          this.isLoading = false;
          this.loadState();
        }
      });
    this.scrollToBottom();

    // Set up modal event handlers
    $(document).ready(() => {
      $('#openModalConfirm').on('hidden.bs.modal', () => {
        this.resetAquaProState();
      });
    });
  }

  ngOnDestroy() {
    // Unsubscribe to avoid memory leaks
    if (this.pageSelectionSubscription) {
      this.pageSelectionSubscription.unsubscribe();
    }
  }

  fetchUserInfo(): void {
    this.userService.getUserInfo().subscribe(
      (data) => {
        this.userName = data.userName || 'Unknown User'; // Fallback for userName
      },
      (error) => {
        console.error('Error fetching user info:', error);
        this.userName = 'Unknown User'; // Fallback on error
      }
    );
  }

  onRandom(index: any) {
    this.threadId = index.toString();
  }

  clipboardState: { [index: number]: boolean } = {};

  onMouseEnter(index: number) {
    this.hoverStates[index] = true;
  }

  onMouseLeave(index: number) {
    this.hoverStates[index] = false;
  }

  // Handle the file selection
  onFileChange(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      this.attachedFile = file; // Save the file to a class property
      this.attachedFileName = file.name;

      // Convert file to Base64
      const reader = new FileReader();
      reader.onload = () => {
        const base64File = (reader.result as string).split(',')[1]; // Extract Base64 part
        this.attachedFileData = base64File; // Save Base64 data for the API body
        console.log(
          'Selected file and converted to Base64:',
          this.attachedFileData
        );
      };
      reader.readAsDataURL(file); // Read the file as a Data URL
    }
  }

  removeAttachment(): void {
    this.attachedFile = null;
    this.attachedFileName = null; // Clear the file name
    this.attachedFileData = null; // Clear the Base64 data
    const fileInput = document.getElementById('fileSelect') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = ''; // Reset the input value
    }
  }

  addPrompt(
    index: number,
    user_ID: number,
    prompt_type: string,
    message: Message
  ) {
    this.logService.log(user_ID + prompt_type);
    this.logService.log(this.messages[index].text);
    if (user_ID && prompt_type) {
      const body = {
        content: this.messages[index].text,
        prompt_type: prompt_type,
        description: '',
        message_id: message.messageID,
      };

      const apiUrl = `${this.url}/prompts/`;
      this.http.post<any>(apiUrl, body).subscribe(
        (response) => {
          this.logService.log('Prompt Added successfully!' + response);
        },
        (error) => {
          this.logService.error('Error adding Prompt' + error);
        }
      );

      this.alertService.showAlert('Prompt Added successfully!', 'success');
    }
  }

  loadState() {
    if (this.isNewChat()) {
      this.isLoading = false;
      this.chatService.clearChatState(); // Clear previous state if it's a new chat
    }

    const savedState = this.chatService.getChatState();
    if (this.buttonNewChat != true) {
      this.isLoading = true;
    } else this.isLoading = false;
    // Set loading to true at the start

    if (savedState) {
      const { isChatActive, userInput, messages } = savedState;
      this.isChatActive = isChatActive;
      this.userInput = userInput;
      this.messages = messages;

      // Process messages and files asynchronously
      this.processMessages(messages).then(() => {
        this.isLoading = false; // Set loading to false once processing is complete
      });
    }
  }

  async processMessages(messages: any[]) {
    for (const message of messages) {
      if (message.extraData && message.extraData.content) {
        this.base64Encoded = message.extraData.content;
        this.fileName = message.extraData.filename;

        try {
          const decodedData = await this.decodeBase64ToExcel(
            this.base64Encoded
          );
          message.excelData = decodedData; // Properly assign the decoded data
        } catch (error) {
          console.error('Error processing Excel data:', error);
        }
      }
    }
  }

  isNewChat(): boolean {
    return !localStorage.getItem('chatState'); // Determine if it's a new chat
  }

  resetChat() {
    this.messages = []; // Clear all previous messages
    this.userInput = ''; // Reset user input
    this.isChatActive = false; // Reset chat active state
    this.previousExternalInput = ''; // Reset previous external input
    this.externalInputProcessed = false; // Reset external input processed flag
    this.saveState(); // Save the reset state to the service
  }
  // Check if there is any external input stored and start the chat
  checkForExternalInput() {
    const externalInput = this.chatService.getExternalInput();
    if (
      externalInput &&
      externalInput !== this.previousExternalInput &&
      !this.externalInputProcessed
    ) {
      this.resetChat();
      this.previousExternalInput = externalInput; // Mark the external input as processed
      this.userInput = externalInput;
      this.externalInputProcessed = true; // Set this flag to true immediately
      this.sendMessage(false); // Flag to indicate this is external input
      this.chatService.clearExternalInput(); // Clear the external input once it's processed
    }
  }

  startChat(suggestion: string) {
    this.userInput = suggestion;
    this.sendMessage();
  }

  sendMessage(isExternalInput: boolean = false) {
    if (this.userInput.trim() || this.attachedFile) {
      // Check if it's external input and ensure it's only processed once
      const convId = localStorage.getItem('convId');
      let messageID: string | null = null;

      if (isExternalInput && !this.externalInputProcessed) {
        this.messages.push({
          text: this.userInput,
          sender: 'user',
          convID: convId ? convId : null,
          messageID: messageID,
          fileName: this.attachedFileName,
        });
        this.externalInputProcessed = true;
      } else {
        this.logService.log(
          'External input already processed:' + this.userInput
        ); // Debug log
      }

      this.isChatActive = true;

      // Call the API for response
      this.fetchResponseFromAPI(this.userInput, convId, this.attachedFile);

      this.userInput = '';
      this.removeAttachment();

      // Save the chat state and scroll to the bottom
      this.cdr.detectChanges();
      this.saveState();
      this.scrollToBottom();
    }

    // Reset the external input flag after processing
    if (isExternalInput) {
      this.logService.log('External input reset flag after processing.');
      this.externalInputProcessed = false; // Allow new external inputs in the future
    }
  }

  onUserMessageClick(message: Message) {
    if (!message.messageID) {
      console.warn('Message ID is not available yet!');
      return; // Prevent action if message ID isn't set
    }
    console.log('Message Clicked:', message.messageID);
  }

  fetchResponseFromAPI(
    userQuestion: string,
    conveId?: any,
    file?: File | null
  ) {
    console.log('attached file', file);
    this.isTyping = true;
    // Add a temporary user message without an ID
    const tempMessage: Message = {
      text: userQuestion,
      sender: 'user',
      convID: conveId || null,
      messageID: null, // Message ID will be updated after API response
    };
    const tempMessageIndex = this.messages.length; // Store index
    this.messages.push(tempMessage);

    this.chatService.sendUserQuestion(userQuestion, conveId, file).subscribe({
      next: async (response) => {
        this.isTyping = false;
        // Handle bot response
        const convId = localStorage.getItem('convId');
        const lastUserMessageID = response.data.last_user_message.message_id;
        // Update the previously added user message with the correct ID
        if (this.messages[tempMessageIndex]) {
          this.messages[tempMessageIndex].messageID = lastUserMessageID;
        }

        const extraData = response.data.messages[0].extra_data;
        const botResponse: Message = {
          text: response.data.response_text, // Adjust to match your API's response structure
          sender: 'bot',
          title: response.data.chat_title,
          convID: !(response.data.conversation_id == convId)
            ? response.data.conversation_id
            : convId,
          thread: response.data.messages,
          messageID: response.data.last_user_message.message_id,
          extraData:
            Object.keys(extraData).length === 0 &&
            extraData.constructor === Object
              ? null
              : response.data.messages[0].extra_data,
        };
        this.chatService.setChatTitle(response.data.chat_title);
        this.messages.push(botResponse);
        if (extraData?.content) {
          try {
            const decodedData = await this.decodeBase64ToExcel(
              extraData.content
            );
            botResponse.excelData = decodedData; // Assign decoded data to bot response
            this.cdr.detectChanges(); // Trigger change detection
          } catch (error) {
            console.error('Error decoding Excel data:', error);
          }
        }
        this.logService.log(
          'testing name' +
            this.chatService.setChatTitle(response.data.chat_title)
        );

        // Check if extraData is an empty object

        this.logService.log('the bot response' + botResponse);

        this.base64Encoded = response.data.messages[0]?.extra_data?.content;
        this.fileName = response.data.messages[0]?.extra_data?.filename;

        if (localStorage.getItem('convId')) {
          this.convData = localStorage.getItem('convId');
        } else {
          localStorage.setItem('convId', response.data.conversation_id);
        }

        this.logService.log('bot response:' + this.messages);
        this.isTyping = false;
        this.saveState(); // Save the updated chat state
        this.cdr.detectChanges();
        this.scrollToBottom();

        console.log(this.fileName);

        this.decodeBase64ToExcel(this.base64Encoded);
      },
      error: (error) => {
        this.logService.error('Failed to fetch bot response:' + error);

        const errorMessage: Message = {
          text: 'Sorry, an error occurred while fetching the response.',
          sender: 'bot',
          convID: this.threadId,
        };

        this.messages.push(errorMessage);

        this.isTyping = false;
        this.saveState(); // Save the state even on error
        this.cdr.detectChanges();
        this.scrollToBottom();
      },
    });
  }

  refreshResponse(index: number, message: Message) {
    this.scrollToBottom();
    this.isrefreshing = true;
    const requestBody = {
      conversation_id: localStorage.getItem('convId'),
      message_id: message.messageID,
    };

    this.isTyping = true; // Show typing indicator before response

    this.http.post(`${this.url}/chat/refresh`, requestBody).subscribe({
      next: async (response: any) => {
        this.isTyping = false;

        if (response && response.data) {
          const extraData = response.data.messages[0]?.extra_data;

          const regeneratedMessage: Message = {
            text: response.data.response_text,
            sender: 'bot',
            title: response.data.chat_title,
            convID: response.data.conversation_id,
            thread: response.data.messages,
            messageID: response.data.last_user_message.message_id,
            extraData: Object.keys(extraData).length ? extraData : null,
          };

          this.chatService.setChatTitle(response.data.chat_title);
          this.messages.push(regeneratedMessage); // Push the new regenerated message

          if (extraData?.content) {
            try {
              const decodedData = await this.decodeBase64ToExcel(
                extraData.content
              );
              regeneratedMessage.excelData = decodedData; // Store decoded data
              this.cdr.detectChanges();
            } catch (error) {
              console.error('Error decoding Excel data:', error);
            }
          }

          this.logService.log(
            'Regenerated response added:' + regeneratedMessage
          );
          this.saveState(); // Save updated chat state
          this.scrollToBottom();
          this.isrefreshing = false;
        }
      },
      error: (error) => {
        this.logService.error('Failed to regenerate response: ' + error);

        const errorMessage: Message = {
          text: 'Sorry, an error occurred while regenerating the response.',
          sender: 'bot',
          convID: localStorage.getItem('convId'),
        };

        this.messages.push(errorMessage);
        this.isTyping = false;
        this.saveState();
        this.cdr.detectChanges();
        this.scrollToBottom();
        this.isrefreshing = false;
      },
    });
  }

  getMsgID() {
    this.logService.log('' + this.messages);
  }

  getConvID() {
    this.logService.log('' + localStorage.getItem('convId'));
  }

  // Method to decode the Base64 string, parse the Excel data, and display it
  decodeBase64ToExcel(base64: string): Promise<any[][]> {
    return new Promise((resolve, reject) => {
      const byteCharacters = atob(base64);
      const byteArrays = [];
      for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
        const slice = byteCharacters.slice(offset, offset + 1024);
        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }
        byteArrays.push(new Uint8Array(byteNumbers));
      }

      const blob = new Blob(byteArrays, {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const reader = new FileReader();

      reader.onload = (e: any) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];

          const newData: any[][] = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
          }) as any[][];

          resolve(newData); // Return the decoded data
        } catch (error) {
          console.error('Error decoding Excel data:', error);
          reject(error);
        }
      };

      reader.onerror = (error) => {
        console.error('FileReader Error:', error);
        reject(error);
      };

      reader.readAsArrayBuffer(blob);
    });
  }

  // Method to download the Excel file from Base64 data
  downloadFile(content: string, fileNameExcel: string): void {
    this.base64Encoded = content;
    if (this.base64Encoded) {
      const byteCharacters = atob(this.base64Encoded);
      const byteArrays = [];

      for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
        const slice = byteCharacters.slice(offset, offset + 1024);
        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }
        byteArrays.push(new Uint8Array(byteNumbers));
      }

      const blob = new Blob(byteArrays, {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = fileNameExcel;
      link.click();
    } else {
      alert('No Base64 data to download!');
    }
  }

  saveState() {
    const state = {
      isChatActive: this.isChatActive,
      userInput: this.userInput,
      messages: this.messages.map((message) => ({
        ...message,
        liked: message.liked, // Store liked status
        disliked: message.disliked, // Store disliked status
      })),
    };
    this.chatService.saveChatState(state);
  }

  clearChat() {
    this.messages = [];
    this.userInput = '';
    this.isChatActive = false;
    this.previousExternalInput = '';
    this.saveState();
  }

  handleButtonAction(action: string) {
    switch (action) {
      case 'openModal':
        // Logic to open a modal
        break;
      case 'commonResponse':
        // Logic for common response action
        break;
      case 'action1':
        // Logic for action 1
        break;
      case 'action2':
        // Logic for action 2
        break;
      case 'action3':
        // Logic for action 3
        break;
      default:
        this.logService.log('Unknown action:' + action);
    }
  }

  // Copy Function
  onClipboardClick(index: number): void {
    const textToCopy = this.messages[index].text; // Retrieve the message text

    if (textToCopy) {
      navigator.clipboard
        .writeText(textToCopy)
        .then(() => {
          this.clipboardState[index] = true;

          setTimeout(() => {
            this.clipboardState[index] = false; // Revert to clipboard icon after 2 seconds
          }, 2000);
        })
        .catch((err) => {
          this.logService.error('Failed to copy text: ' + err); // Log error if clipboard fails
        });
    }
  }
  // Function to track if the user liked/disliked the response

  handleUserFeedback(index: number, feedback: 'like' | 'dislike') {
    const messageID = this.messages[index].messageID;
    if (feedback === 'like') {
      this.messages[index].liked = true;
      this.logService.log(this.messages[index].messageID);
      this.messages[index].disliked = false; // Ensure it's not marked as disliked
    } else if (feedback === 'dislike') {
      this.messages[index].disliked = true;
      this.messages[index].liked = false; // Ensure it's not marked as liked
    }
    // Send feedback to the API
    this.sendFeedbackToApi(messageID, feedback);

    this.saveState(); // Save the state after feedback
    this.logService.log(`Message ${index} ${feedback}`);
  }
  // Function to send the feedback to the API
  private sendFeedbackToApi(messageID: string, feedback: 'like' | 'dislike') {
    const apiUrl = `${this.baseUrl}/${messageID}/${feedback}`; // Construct the API URL with the message ID

    const body = {
      feedback: feedback, // Send the feedback as 'like' or 'dislike'
    };

    // Send the PUT request
    this.http.put<any>(apiUrl, body).subscribe(
      (response) => {
        // Handle the success response
        this.logService.log('API Response:' + response);
        if (response.feedback === 'like') {
          this.logService.log(`Message ${messageID} liked successfully.`);
        }
      },
      (error) => {
        // Handle any error from the API
        this.logService.error('Error liking the message:' + error);
      }
    );
  }

  // Variables to track the current message being sent to AquaPro
  currentAquaProMessageId: string | null = null;
  isProcessingAquaPro: boolean = false;
  aquaProResults: any[] = [];

  // Function to prepare sending data to AquaPro
  prepareAquaProSend(message: Message): void {
    // Log the message object for debugging
    console.log('Message being sent to AquaPro:', message);

    // Check if thread exists and contains the bot message
    if (
      message.thread &&
      Array.isArray(message.thread) &&
      message.thread.length > 0
    ) {
      // Find the bot message in the thread (sender is 'assistant')
      // Use type assertion to handle the TypeScript type issue
      const botMessage = message.thread.find(
        (msg: any) => msg.sender === 'assistant'
      ) as any;

      if (botMessage && botMessage.message_id) {
        // Use the bot's message_id from the thread
        this.currentAquaProMessageId = botMessage.message_id;
        console.log(
          'Using message_id from bot message in thread:',
          botMessage.message_id
        );
        this.logService.log(
          `Preparing to send bot message ${botMessage.message_id} to AquaPro`
        );
        return;
      }
    }

    // If we couldn't find the bot message in the thread, try extraData
    if (message.extraData && message.extraData.message_id) {
      // Use the message_id from extraData
      this.currentAquaProMessageId = message.extraData.message_id;
      console.log(
        'Using message_id from extraData:',
        message.extraData.message_id
      );
      this.logService.log(
        `Preparing to send bot message ${message.extraData.message_id} to AquaPro`
      );
    } else if (message.messageID) {
      // Last resort: fallback to the message's own ID
      this.currentAquaProMessageId = message.messageID;
      console.log('Using messageID from message object:', message.messageID);
      this.logService.log(
        `Preparing to send message ${message.messageID} to AquaPro (fallback)`
      );
    } else {
      // No message ID available
      console.error('No message ID found in message object:', message);
      this.logService.error('Cannot send to AquaPro: Message ID is missing');
      this.alertService.showAlert(
        'Cannot send to AquaPro: Message ID is missing',
        'error'
      );
      return;
    }

    // The modal will be shown via the data-toggle and data-target attributes in the HTML
  }

  // Function to send data to AquaPro after confirmation
  sendToAquaPro(): void {
    if (!this.currentAquaProMessageId) {
      this.logService.error('Cannot send to AquaPro: No message selected');
      this.alertService.showAlert(
        'Cannot send to AquaPro: No message selected',
        'error'
      );
      return;
    }

    this.isProcessingAquaPro = true;
    console.log(
      'Sending to AquaPro with message ID:',
      this.currentAquaProMessageId
    );

    // Call the AquaPro Integration API
    this.chatService.createAquaProData(this.currentAquaProMessageId).subscribe({
      next: (response) => {
        console.log('AquaPro API response:', response);
        this.logService.log('AquaPro API response received');

        // Parse the response to get the integration results
        const results = parseAquaProResponse(response);
        console.log('Parsed AquaPro results:', results);

        // Store the results for display
        this.aquaProResults = results;

        // Create a summary message
        let summaryMessage = 'Successfully sent to AquaPro';

        // If we have results, add a summary
        if (results.length > 0) {
          const successCount = results.filter(
            (r) => r.status === 'success'
          ).length;
          summaryMessage = `AquaPro integration completed: ${successCount}/${results.length} operations successful`;
          console.log(`Success count: ${successCount}/${results.length}`);
        }

        // Show success message
        this.alertService.showAlert(summaryMessage, 'success');
        this.isProcessingAquaPro = false;

        // Close the modal programmatically
        $('#openModalConfirm').modal('hide');
      },
      error: (error) => {
        console.error('AquaPro API error:', error);
        this.logService.error(
          `Error sending to AquaPro: ${JSON.stringify(error)}`
        );

        // Create a more detailed error message
        let errorMessage = 'Failed to send to AquaPro. Please try again.';

        // Check if there's a specific error message from the API
        if (error && error.error && error.error.message) {
          errorMessage = `AquaPro error: ${error.error.message}`;
        } else if (error && error.message) {
          errorMessage = `AquaPro error: ${error.message}`;
        }

        this.alertService.showAlert(errorMessage, 'error');
        this.isProcessingAquaPro = false;

        // Close the modal programmatically
        $('#openModalConfirm').modal('hide');
      },
    });
  }

  // Function to cancel sending to AquaPro
  cancelAquaProSend(): void {
    this.currentAquaProMessageId = null;
    this.aquaProResults = [];

    // Close the modal programmatically
    $('#openModalConfirm').modal('hide');
  }

  // Function to reset AquaPro state
  resetAquaProState(): void {
    this.currentAquaProMessageId = null;
    this.isProcessingAquaPro = false;
    this.aquaProResults = [];
  }

  formatToHTML(text: string): string {
    // Replace ordered list items (e.g., "1. Item") with <li>
    // text = text.replace(
    //   /^(\d+)\.\s+(.*?)(?=\n|$)/gm,
    //   (_, number, content) => `<li>${content.trim()}</li>`
    // );

    // Wrap consecutive <li> elements for ordered lists in <ol>
    // text = text.replace(
    //   /(<li>.*?<\/li>(\n|$))+/gm,
    //   (match) => `<ol>${match.trim()}</ol>`
    // );

    // Replace unordered list items (e.g., "- Item") with <li>
    text = text.replace(
      /^-\s+(.*?)(?=\n|$)/gm,
      (_, content) => `<li>${content.trim()}</li>`
    );

    // Wrap consecutive <li> elements for unordered lists in <ul>
    text = text.replace(
      /(<li>.*?<\/li>(\n|$))+/gm,
      (match) => `<ul>${match.trim()}</ul>`
    );

    // Convert headings (e.g., "### Heading") to respective HTML heading tags
    text = text.replace(/^###\s+(.*?)(?=\n|$)/gm, '<h3>$1</h3>');
    text = text.replace(/^##\s+(.*?)(?=\n|$)/gm, '<h2>$1</h2>');
    text = text.replace(/^#\s+(.*?)(?=\n|$)/gm, '<h1>$1</h1>');

    // Replace double asterisks with <strong> for bold text
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Add <br> tags for single newlines outside of lists and headings
    text = text.replace(/(?<!<\/(li|h[1-3])>)\n(?!<(li|h[1-3]))/g, '<br>');

    // Trim and return the final formatted HTML
    return text.trim();
  }

  scrollToBottom() {
    setTimeout(() => {
      this.chatContainer.nativeElement.scrollTop =
        this.chatContainer.nativeElement.scrollHeight;
    }, 0);
  }

  pinChat(): void {
    const chatId: string | null = localStorage.getItem('convId');
    const apiUrl = `${this.pinUrl}/${chatId}/pin/?attempt=1`; // Construct API URL

    this.http.put(apiUrl, { withCredentials: true }).subscribe({
      next: (response: any) => {
        const isPinned = response.pinned; // Extract pinned status from the response
        const message = isPinned
          ? 'Chat pinned successfully!'
          : 'Chat unpinned successfully!';
        this.alertService.showAlert(message, isPinned ? 'success' : 'warning'); // Show alert based on status
        console.log('Response:', response);
      },
      error: (error) => {
        console.error('Error pinning chat:', error);
        this.alertService.showAlert(
          'Failed to pin chat. Please try again.',
          'error'
        ); // Show error message
      },
    });
  }

  // Function to get table headers from tableData
  getTableHeaders(tableData: TableRow[]): string[] {
    if (!tableData || tableData.length === 0) {
      return [];
    }

    // Get all unique keys from all rows, excluding the 'visible' property
    const headers = new Set<string>();
    tableData.forEach((row) => {
      Object.keys(row).forEach((key) => {
        if (key !== 'visible') {
          headers.add(key);
        }
      });
    });

    return Array.from(headers);
  }

  // Function to get cell CSS class based on content type (generic approach)
  getCellTypeClass(content: any): string {
    if (!content) {
      return 'cell-empty';
    }

    const stringContent = String(content).trim();

    // Check if it's a number (including ratings)
    if (!isNaN(Number(stringContent)) && stringContent !== '') {
      return 'cell-numeric';
    }

    // Check if it's a short identifier (like operation numbers, codes)
    if (stringContent.length <= 10 && !stringContent.includes('\n')) {
      return 'cell-identifier';
    }

    // Check if it's multi-line content
    if (stringContent.includes('\n')) {
      return 'cell-multiline';
    }

    // Check if it's a long description
    if (stringContent.length > 50) {
      return 'cell-description';
    }

    // Default for short text
    return 'cell-text';
  }

  // Function to format cell content (handle line breaks and special formatting)
  formatCellContent(content: any): string {
    if (!content) {
      return '';
    }

    const stringContent = String(content);

    // Replace newlines with <br> tags for proper display
    return stringContent.replace(/\n/g, '<br>');
  }

  // Function to check if a cell should be merged (same content as previous row)
  shouldMergeCell(
    tableData: TableRow[],
    rowIndex: number,
    header: string
  ): boolean {
    if (rowIndex === 0) return false;

    const currentValue = tableData[rowIndex][header];
    const previousValue = tableData[rowIndex - 1][header];

    // Merge if values are the same and not empty
    return !!(
      currentValue === previousValue &&
      currentValue &&
      String(currentValue).trim() !== ''
    );
  }

  // Function to check if this is the first occurrence of a value in a column
  isFirstOccurrence(
    tableData: TableRow[],
    rowIndex: number,
    header: string
  ): boolean {
    if (rowIndex === 0) return true;

    const currentValue = tableData[rowIndex][header];
    const previousValue = tableData[rowIndex - 1][header];

    return currentValue !== previousValue;
  }

  // Function to count consecutive rows with same value
  getRowSpan(
    tableData: TableRow[],
    startRowIndex: number,
    header: string
  ): number {
    const startValue = tableData[startRowIndex][header];
    let count = 1;

    for (let i = startRowIndex + 1; i < tableData.length; i++) {
      if (tableData[i][header] === startValue) {
        count++;
      } else {
        break;
      }
    }

    return count;
  }
}
