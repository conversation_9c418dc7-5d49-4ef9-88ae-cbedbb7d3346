<div class="d-flex flex-colum align-items-center py-2 px-2" *ngIf="isLoading">
  <div
    class="col justify-content-center text-center align-items-center flex-fill"
  >
    <div class="text-center">
      <p>Loading...</p>
    </div>
  </div>
</div>

<div
  class="d-flex flex-colum align-items-center py-2 px-2"
  *ngIf="totalCount == 0 && !isLoading"
>
  <div
    class="col justify-content-center text-center align-items-center flex-fill"
  >
    <div class="text-center">
      <h6>No Chats to Show</h6>
    </div>
  </div>
</div>

<div *ngFor="let date of getKeys(groupedEntries)" class="mb-3">
  <h6>{{ date }}</h6>

  <hr />
  <div *ngFor="let entry of groupedEntries[date]; let i = index">
    <div class="col px-0 d-flex pb-2 cursor-pointer">
      <div class="card flex-fill border-0 shadow1">
        <div class="card-body p-2">
          <div class="d-flex flex-row justify-content-between">
            <div
              class="px-2 mb-0 body2 ellipsis text-nowrap"
              (click)="startChat(entry.chat_id); toChatPage('New Chat')"
            >
              {{ entry.title }}
            </div>
            <div class="col-auto px-0 d-flex flex-row">
              <div
                class="col px-1 hover cursor-pointer"
                data-toggle="modal"
                data-target="#exampleModal"
                (click)="startEdit(i)"
              >
                <img src="{{ assetBasePath }}Icon/chat/NotePencil.png" />
              </div>
              <div
                class="col px-1 table-hover cursor-pointer"
                (click)="deleteEntry(i)"
              >
                <img src="{{ assetBasePath }}Icon/chat/Trash.svg" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Pagination Component -->
<app-pagination
  [totalCount]="totalCount"
  [itemsPerPage]="itemsPerPage"
  [currentPage]="currentPage"
  (pageChange)="onPageChange($event)"
></app-pagination>

<!-- Modal -->
<div
  class="modal fade"
  id="exampleModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-primary">
        <h5 class="modal-title text-white" id="exampleModalLabel">
          Edit Chat Name
        </h5>
        <button
          type="button"
          class="close text-white"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="pb-4">{{ editedName }}</div>
        <div class="pt-2">
          <input
            class="form-control bg-"
            type="text"
            placeholder="Edit Chat Name"
            [(ngModel)]="editedName"
          />
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-sm btn-outline-primary"
          data-dismiss="modal"
        >
          Close
        </button>
        <button
          type="button"
          class="btn btn-primary btn-sm"
          (click)="saveEdit()"
          data-dismiss="modal"
        >
          Save
        </button>
      </div>
    </div>
  </div>
</div>
