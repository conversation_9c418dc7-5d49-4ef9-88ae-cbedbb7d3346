import { AquaProIntegrationResult, AquaProResponse, AquaProResponseContent } from '../interfaces/aquapro-response.interface';

/**
 * Parses the AquaPro API response string into a structured object
 * @param response The AquaPro API response
 * @returns The parsed integration results
 */
export function parseAquaProResponse(response: AquaProResponse): AquaProIntegrationResult[] {
  try {
    if (!response.success) {
      console.error('AquaPro API request was not successful');
      return [];
    }

    // Parse the response string into an object
    const parsedResponse: AquaProResponseContent = JSON.parse(response.response);
    
    // Return the integration results
    return parsedResponse.integration_results || [];
  } catch (error) {
    console.error('Error parsing AquaPro response:', error);
    return [];
  }
}

/**
 * Checks if the AquaPro integration was successful
 * @param results The integration results
 * @returns True if all operations were successful
 */
export function isAquaProIntegrationSuccessful(results: AquaProIntegrationResult[]): boolean {
  if (!results || results.length === 0) {
    return false;
  }
  
  // Check if all operations have a status of 'success'
  return results.every(result => result.status === 'success');
}
