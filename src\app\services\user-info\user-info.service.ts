import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of, map } from 'rxjs';
import { environment } from 'src/environments/environment.development';

@Injectable({
  providedIn: 'root',
})
export class UserInfoService {
  url: string = environment.apiUrl;
  private apiUrl = `${this.url}/users/get-user-info?attempt=1`;

  constructor(private http: HttpClient) {}

  getUserInfo(): Observable<{ userName: string; userPhoto: string | null }> {
    const storedToken = sessionStorage.getItem('sessionToken');
    const currentToken = this.getSessionTokenFromCookie();

    if (storedToken !== currentToken) {
      this.clearUserInfo();
      sessionStorage.setItem('sessionToken', currentToken);
      return this.fetchAndStoreUserInfo();
    }

    const userName = sessionStorage.getItem('userName');
    const userPhoto = sessionStorage.getItem('userPhoto');

    if (userName) {
      return of({
        userName,
        userPhoto: this.formatBase64Image(userPhoto),
      });
    } else {
      return this.fetchAndStoreUserInfo();
    }
  }

  private fetchAndStoreUserInfo(): Observable<{
    userName: string;
    userPhoto: string | null;
  }> {
    return this.http.get<any>(this.apiUrl, { withCredentials: true }).pipe(
      map((response) => {
        const userPhoto = response.userPhoto || null; // Use null if no photo is provided
        const formattedPhoto = userPhoto
          ? this.formatBase64Image(userPhoto)
          : null;

        const userInfo = {
          userName: response.userName || 'Unknown User',
          userPhoto: formattedPhoto,
        };

        sessionStorage.setItem('userName', userInfo.userName);
        sessionStorage.setItem('userPhoto', userPhoto || ''); // Store raw base64 value or empty string

        return userInfo;
      })
    );
  }

  private formatBase64Image(base64String: string | null): string | null {
    return base64String ? `data:image/jpeg;base64,${base64String}` : null;
  }

  clearUserInfo(): void {
    sessionStorage.removeItem('userName');
    sessionStorage.removeItem('userPhoto');
  }

  private getSessionTokenFromCookie(): string {
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [key, value] = cookie.split('=').map((c) => c.trim());
      if (key === 'ASP.NET_SessionId_Shared') {
        return value;
      }
    }
    return '';
  }
}
