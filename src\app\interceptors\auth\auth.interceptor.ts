import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { CookieService } from 'src/app/services/cookie/cookie.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private cookieService: CookieService) {}

  intercept(
    request: HttpRequest<unknown>,
    next: <PERSON>ttpHand<PERSON>
  ): Observable<HttpEvent<unknown>> {
    const clonedRequest = request.clone({
      withCredentials: true, // Automatically sends HttpOnly cookies
    });
    return next.handle(clonedRequest);
  }
}
