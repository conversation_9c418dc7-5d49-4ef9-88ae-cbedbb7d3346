import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

export type AlertType = 'success' | 'warning' | 'error' | 'info'; // Alert types
@Injectable({
  providedIn: 'root',
})
export class AlertService {
  private alertMessageSource = new Subject<{
    message: string;
    type: AlertType;
    duration?: number;
  }>();
  alertMessage$ = this.alertMessageSource.asObservable();
  private colorMapping: { [key in AlertType]: string } = {
    success: '#0CC784', // Green
    warning: '#FFA500', // Orange
    error: '#FF5733', // Red
    info: '#17A2B8', // Blue
  };
  constructor() {}

  showAlert(message: string, type: AlertType, duration: number = 3000) {
    const color = this.getColor(type);
    this.alertMessageSource.next({ message, type, duration });
  }

  // Function to get the color based on the alert type
  private getColor(type: AlertType): string {
    return this.colorMapping[type] || '#000000'; // Default to black if type is not found
  }

  // Optional method to clear the message
  clearAlert() {
    this.alertMessageSource.next({ message: '', type: 'info' });
  }
}
