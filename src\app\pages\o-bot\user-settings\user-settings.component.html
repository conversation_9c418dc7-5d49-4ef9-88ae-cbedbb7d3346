<div>
  <div class="card">
    <div class="card-body px-0">
      <div class="d-flex flex-row pb-3 align-items-center px-2">
        <div
          *ngIf="!userPhoto"
          class="avatar rounded-circle d-flex justify-content-center align-items-center border-primary"
          [style.background]="avatarColor"
          style="height: 60px; width: 60px"
        >
          {{ initials }}
        </div>
        <div
          class="avatar rounded-circle d-flex justify-content-center align-items-center"
          *ngIf="userPhoto != null"
        >
          <img
            *ngIf="userPhoto"
            [src]="userPhoto"
            alt="User Photo"
            width="60px"
            height="60px"
            class="avatar rounded-circle d-flex justify-content-center align-items-center"
          />
        </div>

        <div>
          <h5 class="text-black pl-2">{{ userName }}</h5>

          <!-- <div class="body1">jihndoekasdnjnasdcom</div> -->
        </div>
      </div>
      <hr />
      <div class="container-fluid width-100">
        <div class="row justify-content-between align-items-center px-2">
          <div>
            <h6 class="px-0 mb-0">Theme</h6>
          </div>
          <div class="d-flex flex-row align-items-center py-0 px-2">
            <div class="dropdown">
              <button
                class="btn btn-sm btn-outline-light dropdown-toggle"
                type="button"
                id="dropdownMenuButton"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
              >
                {{ selectedTheme }}
              </button>
              <div
                class="dropdown-menu body1 dropdown-menu-right py-0"
                aria-labelledby="dropdownMenuButton"
              >
                <div
                  class="dropdown-item px-1 cursor-pointer"
                  (click)="changeTheme('System')"
                  [ngClass]="{ active: selectedTheme === 'System' }"
                >
                  <div
                    class="row mx-0 justify-content-between align-items-center"
                  >
                    <div>
                      <img
                        [src]="
                          selectedTheme === 'System'
                            ? themeIcons.System.active
                            : themeIcons.System.regular
                        "
                      />
                      System
                    </div>
                    <div *ngIf="selectedTheme === 'System'">
                      <img src="{{ assetBasePath }}Icon/Check-primary.svg" />
                    </div>
                  </div>
                </div>
                <div
                  class="dropdown-item px-1 cursor-pointer"
                  (click)="changeTheme('Light')"
                  [ngClass]="{ active: selectedTheme === 'Light' }"
                >
                  <div
                    class="row mx-0 justify-content-between align-items-center"
                  >
                    <div>
                      <img
                        [src]="
                          selectedTheme === 'Light'
                            ? themeIcons.Light.active
                            : themeIcons.Light.regular
                        "
                      />
                      Light
                    </div>
                    <div *ngIf="selectedTheme === 'Light'">
                      <img src="{{ assetBasePath }}Icon/Check-primary.svg" />
                    </div>
                  </div>
                </div>
                <div
                  class="dropdown-item px-1 cursor-pointer"
                  (click)="changeTheme('Dark')"
                  [ngClass]="{ active: selectedTheme === 'Dark' }"
                >
                  <div
                    class="row mx-0 justify-content-between align-items-center"
                  >
                    <div>
                      <img
                        [src]="
                          selectedTheme === 'Dark'
                            ? themeIcons.Dark.active
                            : themeIcons.Dark.regular
                        "
                      />
                      Dark
                    </div>
                    <div *ngIf="selectedTheme === 'Dark'">
                      <img src="{{ assetBasePath }}Icon/Check-primary.svg" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row justify-content-between align-items-center px-2 py-3">
          <div>
            <h6 class="px-0 mb-0">Role</h6>
          </div>
          <div class="d-flex flex-row align-items-center py-0 px-2">
            <span class="badge badge-pill badge-primary">User</span>
          </div>
        </div>
      </div>
      <div></div>
    </div>
  </div>
</div>
