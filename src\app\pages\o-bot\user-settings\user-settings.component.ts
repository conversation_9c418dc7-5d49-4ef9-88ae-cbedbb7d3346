import { Component } from '@angular/core';
import { SessionTokenService } from 'src/app/services/sessionToken/session-token.service';
import { ThemeSwitchService } from 'src/app/services/theme/theme-switch.service';
import { UserInfoService } from 'src/app/services/user-info/user-info.service';
import { environment } from 'src/environments/environment.development';

@Component({
  selector: 'app-user-settings',
  templateUrl: './user-settings.component.html',
  styleUrls: ['./user-settings.component.css'],
})
export class UserSettingsComponent {
  selectedTheme: string = 'System'; // Default theme
  assetBasePath: string = environment.assetBasePath;

  constructor(
    private themeService: ThemeSwitchService,
    private userService: UserInfoService,
    private sessionTokenService: SessionTokenService
  ) {
    this.syncTheme();
  }
  // Inside your component
  themeIcons = {
    System: {
      active: `${this.assetBasePath}Icon/settings/Monitor-primary.svg`,
      regular: `${this.assetBasePath}Icon/settings/Monitor.svg`,
    },
    Light: {
      active: `${this.assetBasePath}Icon/settings/Sun-primary.svg`,
      regular: `${this.assetBasePath}Icon/settings/Sun.svg`,
    },
    Dark: {
      active: `${this.assetBasePath}Icon/settings/Moon-primary.svg`,
      regular: `${this.assetBasePath}Icon/settings/Moon.svg`,
    },
  };

  userName: string = '';
  userPhoto: string | null = '';
  initials: string = '';
  avatarColor: string = '';

  ngOnInit() {
    this.sessionTokenService.checkForTokenChange();
    this.fetchUserInfo();
    this.avatarColor = this.getRandomColor();
  }

  fetchUserInfo(): void {
    this.userService.getUserInfo().subscribe(
      (data) => {
        this.userName = data.userName || 'Unknown User'; // Fallback for userName
        this.userPhoto = data.userPhoto || null; // Handle null photo gracefully
        this.initials = this.getInitials(this.userName);

        console.log(
          'Photo:',
          this.userPhoto ? 'Photo loaded' : 'No photo available'
        );
      },
      (error) => {
        console.error('Error fetching user info:', error);
        this.userName = 'Unknown User'; // Fallback on error
        this.userPhoto = null; // No photo on error
        this.initials = this.getInitials(this.userName);
      }
    );
  }

  getInitials(name: string): string {
    const names = name.split(' ');
    const initials = names.map((n) => n.charAt(0)).join('');
    return initials.length > 2 ? initials.slice(0, 2) : initials; // Limit to 2 characters
  }

  getRandomColor(): string {
    const colors = ['#ff5733', '#33c3ff', '#28a745', '#ffc107', '#6f42c1'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  syncTheme(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    this.selectedTheme = localStorage.getItem('theme') || 'System';
    if (this.selectedTheme === 'System') {
      this.selectedTheme = currentTheme === 'dark' ? 'Dark' : 'Light';
    }
  }

  changeTheme(theme: string): void {
    this.selectedTheme = theme;
    if (theme === 'System') {
      this.themeService.applySystemTheme();
    } else {
      this.themeService.applyTheme(theme.toLowerCase() as 'light' | 'dark');
    }
    this.syncTheme();
  }
}
