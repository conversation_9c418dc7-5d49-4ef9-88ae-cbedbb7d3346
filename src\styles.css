/* You can add global styles to this file, and also import other style files */

/* Import Open Sans from Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap");
@import "~@ng-select/ng-select/themes/default.theme.css";

:root {
  /* Primary Colors */
  --primary: #3abaab !important;
  --primary-100: #4cbfb1 !important;
  --primary-200: #61c8bc !important;
  --primary-300: #75cfc4 !important;
  --primary-400: #89d6cd !important;
  --primary-500: #9cdcd5 !important;
  --primary-600: #b0e3dd !important;
  --primary-700: #c4eae6 !important;
  --primary-800: #ebf8f7 !important;

  /* Black Shades */
  --black: #333333 !important;
  --black-100: #666666 !important;
  --black-200: #808080 !important;
  --black-300: #999999 !important;
  --black-400: #cfcfcf !important;
  --black-500: #e5e6e7 !important;
  --black-600: #efefef !important;
  --black-700: #f5f5f5 !important;

  /* Additional Colors */
  --white: #ffffff !important;
  --warning: #ffde17 !important;
  --positive: #61ae24 !important;
  --negative: #ef4136 !important;

  /* Text and Border Colors */
  --text-primary: #333333 !important;
  --text-secondary: #666666 !important;
  --inactive: #999999 !important;
  --text-field: #cfcfcf !important;
  --divider: #e5e6e7 !important;
  --disabled-background: #f9f9fa !important;
  --background: #f9f9fa;
}

[data-theme="light"] svg {
  fill: var(--white);
}

[data-theme="dark"] svg {
  fill: var(--text-primary);
}

/* Dark Theme Overrides */
[data-theme="dark"] {
  /* Primary Colors */
  --primary: #0cc784;
  --primary-100: #0da572;
  --primary-200: #0e9265;
  --primary-300: #107e59;
  --primary-400: #126b4e;
  --primary-500: #155844;
  --primary-600: #17463a;
  --primary-700: #1a3330;
  --primary-800: #1d201f;

  /* Neutral Colors */
  --black: #e0e0e0;
  --black-100: #b0b0b0;
  --black-200: #909090;
  --black-300: #707070;
  --black-400: #505050;
  --black-500: #383838;
  --black-600: #2a2a2a;
  --black-700: #1c1c1c;

  /* Text and Background */
  --text-primary: #e0e0e0;
  --text-secondary: #b0b0b0;
  --background: #121212;
  --divider: #383838;

  /* Other Colors */
  --warning: #ffbf00;
  --positive: #5cae24;
  --negative: #e84136;
}

body {
  font-family: "Open Sans", sans-serif;
  color: var(--text-primary);
  background-color: var(--background);
  transition: color 0.3s, background-color 0.3s; /* Smooth transition for theme switch */
}

.card {
  background-color: var(--background) !important;
}
.modal-content {
  background-color: var(--background) !important;
  border-color: var(--divider);
}
.modal-footer {
  border-color: var(--divider);
}

/* Headings */
/* Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--text-primary);
}

h1 {
  font-weight: 300;
  font-size: 44px;
  line-height: 54px;
  letter-spacing: 0.2px;
}

h2 {
  font-weight: 500;
  font-size: 32px;
  line-height: 40px;
  letter-spacing: 0px;
}

h3 {
  font-weight: 600;
  font-size: 24px;
  line-height: 30px;
  letter-spacing: 0.1px;
}

h4 {
  font-weight: 500;
  font-size: 20px;
  line-height: 26px;
  letter-spacing: 0.2px;
}

h5 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: 0.2px;
}

h6 {
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0.2px;
}

/* Subtitles */
.subtitle1 {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.1px;
}

.subtitle2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  letter-spacing: 0.1px;
}

/* Body Text */
.body1 {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.1px;
}

.body2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0.1px;
}

/* Small Text */
.small1 {
  font-weight: 500;
  font-size: 13px;
  line-height: 16px;
  letter-spacing: 0.2px;
}

.small2 {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.2px;
}

.small3 {
  font-weight: 400;
  font-size: 11px;
  line-height: 14px;
  letter-spacing: 0.2px;
}
.small4 {
  font-weight: 400;
  font-size: 10px;
  letter-spacing: 0.2px;
}

/* Button Text */
.button {
  font-weight: 600;
  font-size: 14px;
  line-height: 18px;
  letter-spacing: 0px;
}

/* Initials */
.initials {
  font-weight: 600;
  font-size: 13px;
  line-height: 16px;
  letter-spacing: 0.2px;
}

/* Background Colors */
.bg-primary {
  background-color: var(--primary) !important;
}

[data-theme="dark"] .bg-primary {
  background-color: var(--background) !important;
}

.bg-primary.dark-mode {
  background-color: var(--primary) !important;
}
.bg-primary-100 {
  background-color: var(--primary-100) !important;
}
.bg-primary-200 {
  background-color: var(--primary-200) !important;
}

[data-theme="dark"] .bg-primary-200 {
  background-color: var(--primary-800) !important;
}

.bg-primary-300 {
  background-color: var(--primary-300) !important;
}
.bg-primary-400 {
  background-color: var(--primary-400) !important;
}
.bg-primary-500 {
  background-color: var(--primary-500) !important;
}
.bg-primary-600 {
  background-color: var(--primary-600) !important;
}
.bg-primary-700 {
  background-color: var(--primary-700) !important;
}
.bg-primary-800 {
  background-color: var(--primary-800) !important;
}

.bg-black-400 {
  background-color: var(--black-500);
}

/* Text Colors */
.text-primary {
  color: var(--primary) !important;
}
.text-primary-100 {
  color: var(--primary-100);
}
.text-primary-200 {
  color: var(--primary-200);
}
.text-primary-300 {
  color: var(--primary-300);
}
.text-primary-400 {
  color: var(--primary-400);
}
.text-primary-500 {
  color: var(--primary-500);
}
.text-primary-600 {
  color: var(--primary-600);
}
.text-primary-700 {
  color: var(--primary-700);
}
.text-primary-800 {
  color: var(--primary-800);
}

.text-black {
  color: var(--title-text) !important;
}

.text-secondary {
  color: var(--secondary-text) !important;
}

.text-inactive {
  color: var(--inactive) !important;
}

.text-field {
  color: var(--text-field) !important;
}

.text-divider {
  color: var(--divider) !important;
}

/* Border Colors */

.border-primary {
  border: 1px solid var(--primary) !important;
}
.border-primary-100 {
  border: 1px solid var(--primary-100);
}
.border-primary-200 {
  border: 1px solid var(--primary-200);
}
.border-primary-300 {
  border: 1px solid var(--primary-300);
}
.border-primary-400 {
  border: 1px solid var(--primary-400);
}
.border-primary-500 {
  border: 1px solid var(--primary-500);
}
.border-primary-600 {
  border: 1px solid var(--primary-600);
}
.border-primary-700 {
  border: 1px solid var(--primary-700);
}
.border-primary-800 {
  border: 1px solid var(--primary-800);
}

/* Hover Background Colors */
.hover-bg-primary:hover {
  background-color: var(--primary);
}
.hover-bg-primary-100:hover {
  background-color: var(--primary-100);
}
.hover-bg-primary-200:hover {
  background-color: var(--primary-200);
}

[data-theme="dark"] .hover-bg-primary-200:hover {
  background-color: var(--primary-800) !important;
}
.hover-bg-primary-300:hover {
  background-color: var(--primary-300);
}
.hover-bg-primary-400:hover {
  background-color: var(--primary-400);
}
.hover-bg-primary-500:hover {
  background-color: var(--primary-500);
}
.hover-bg-primary-600:hover {
  background-color: var(--primary-600);
}
.hover-bg-primary-700:hover {
  background-color: var(--primary-700);
}
.hover-bg-primary-800:hover {
  background-color: var(--primary-800);
}

/* Hover Text Colors */
.hover-text-primary:hover {
  color: var(--primary);
}
.hover-text-primary-100:hover {
  color: var(--primary-100);
}
.hover-text-primary-200:hover {
  color: var(--primary-200);
}
.hover-text-primary-300:hover {
  color: var(--primary-300);
}
.hover-text-primary-400:hover {
  color: var(--primary-400);
}
.hover-text-primary-500:hover {
  color: var(--primary-500);
}
.hover-text-primary-600:hover {
  color: var(--primary-600);
}
.hover-text-primary-700:hover {
  color: var(--primary-700);
}
.hover-text-primary-800:hover {
  color: var(--primary-800);
}

.btn {
  transition: box-shadow 0.3s ease;
}
.btn:hover {
  box-shadow: 0 1px 4px rgba(12, 12, 13, 0.1);
}

.hover {
  transition: box-shadow 0.3s ease;
}
.hover:hover {
  box-shadow: 0 1px 4px rgba(12, 12, 13, 0.1);
}

.btn-primary {
  color: #fff;
  background-color: var(--primary);
  border-color: var(--primary);
}
.btn-primary:hover {
  color: #fff;
  border-color: var(--primary-100);
  background-color: var(--primary-100);
}

.dark-blue {
  color: #113f8c; /* Text color */
}

[data-theme="dark"] .dark-blue {
  color: #fff;
}

.width-100 {
  width: 100%;
}

.btn-dark-blue {
  color: var(--text-primary); /* Text color */
  background-color: #113f8c;
}
.btn .btn-dark-blue:hover {
  color: var(--text-primary);
}
.btn-outline-primary {
  color: var(--primary);
  border-color: var(--primary);
}
.btn-outline-light {
  color: var(--text-primary);
  border-color: none;
}
.btn-outline-primary:hover {
  color: var(--text-primary);
  border-color: var(--primary);
  background-color: var(--primary);
}
.btn-outline-primary:active {
  color: var(--text-primary);
  background-color: var(--primary);
}
.btn-outline-primary:not(:disabled):not(.disabled):active {
  background-color: var(--primary);
}
hr {
  border-top: 1px solid var(--divider);
}

.cursor-pointer {
  cursor: pointer;
}

.badge-primary {
  color: #fff;
  background-color: var(--primary);
}
.badge {
  font-weight: 500;
}
.shadow1 {
  box-shadow: 0px 4px 20px rgba(58, 186, 171, 0.2); /* X, Y, Blur, Color with opacity */
}
.shadow-lg {
  box-shadow: 0 16px 32px -4px rgba(12, 12, 13, 0.1);
}
.ellipsis {
  white-space: nowrap; /* Prevents the text from wrapping */
  overflow: hidden; /* Hides any overflow text */
  text-overflow: ellipsis; /* Adds ellipsis (...) for overflow text */
}
.initials-circle {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0e0e0;
  color: #757575;
  font-weight: bold;
  border-radius: 50%;
  font-size: 16px;
}
.display-hover {
  display: none;
}
.display-hover:hover {
  display: 1;
}
.input-query {
  position: fixed;
  bottom: 28px;
  transform: translateX(-50%);
  z-index: 1;
  max-width: calc(100% - 74%);
  left: calc(100% - 14.5%);
}
.input-query.fullscreen-view {
  position: fixed;
  bottom: 28px;
  left: calc(100% - (100% - 168px) / 2); /* Center horizontally */
  transform: translateX(-48.5%); /* Center offset adjustment */
  z-index: 1;
  max-width: 74%;
}

@media (max-width: 767px) {
  .input-query {
    max-width: 92%;
    transform: translateX(-71.5%);
    left: 68%;
  }
}
.table {
  color: var(--text-primary);
}
.table-striped tbody tr:nth-of-type(2n + 1) {
  background-color: var(--black-600);
}
.table thead th {
  border-color: var(--divider);
}
.grayscale {
  --tw-grayscale: grayscale(0%);
}
.table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
/* app.component.css */
/* Default Light Mode */
.mainContainer {
  background-color: white;
  color: black;
  scroll-behavior: smooth;
}

/* Dark Mode Styles */
.dark-mode {
  background-color: #333;
  color: white;
}

.dark-mode::-webkit-scrollbar {
  width: 8px;
}

.dark-mode::-webkit-scrollbar-thumb {
  background-color: #555;
  border-radius: 4px;
}

.dark-mode::-webkit-scrollbar-track {
  background: #333;
}

/* Smooth Scroll */
html,
body {
  scroll-behavior: smooth;
}

.pagination-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;
}

.pagination-btn {
  background-color: #f3f3f3;
  border: 1px solid #ddd;
  padding: 5px 10px;
  cursor: pointer;
}

.pagination-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.pagination-number {
  padding: 3px 10px;
  cursor: pointer;
  border: 1px solid var(--primary);
}

.pagination-number.active {
  background-color: var(--primary);
  color: #fff;
  font-weight: bold;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-info input {
  width: 60px;
  padding: 5px;
  text-align: center;
}
