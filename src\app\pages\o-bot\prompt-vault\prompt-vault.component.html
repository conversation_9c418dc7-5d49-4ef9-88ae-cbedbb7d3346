<div class="d-flex flex-colum align-items-center py-2 px-2" *ngIf="isLoading">
  <div
    class="col justify-content-center text-center align-items-center flex-fill"
  >
    <div class="text-center">
      <p>Loading...</p>
    </div>
  </div>
</div>
<div *ngIf="!isLoading">
  <div
    *ngIf="
      viewAllState['global_alias'] ||
      (!viewAllState['global_alias'] &&
        !viewAllState['personal'] &&
        !viewAllState['shared'])
    "
    class="mb-3"
  >
    <div class="d-flex flex-row justify-content-between align-items-center">
      <div class="d-flex flex-row align-items-center">
        <h6 class="mb-0">ORGANIZATION</h6>
        <span
          class="btn btn-sm btn-primary ml-2"
          data-toggle="modal"
          data-target="#orgPrompt"
          (click)="startEdit(1)"
          >Add</span
        >
      </div>
      <h6
        class="mb-0 cursor-pointer"
        (click)="
          togglePromptLimit('global_alias'); selectedPromptType('global_alias')
        "
        *ngIf="globalPromptTotal >= 2"
      >
        {{ viewAllState["global_alias"] ? "View Less" : "View All" }}
      </h6>
    </div>
    <hr />
    <div
      class="d-flex flex-colum align-items-center py-2 px-2"
      *ngIf="globalPromptTotal == 0"
    >
      <div
        class="col justify-content-center text-center align-items-center flex-fill"
      >
        <div class="text-center">
          <h6>No Organization Prompts to Show</h6>
        </div>
      </div>
    </div>
    <div *ngFor="let prompt of promptss; let i = index">
      <div class="pb-2" *ngIf="prompt.type == 'global_alias'">
        <div class="card flex-fill border-0 shadow1">
          <div class="card-body p-2">
            <div class="d-flex flex-row align-items-center">
              <div class="col px-2 mb-0 body2 ellipsis text-nowrap">
                <h6 class="ellipsis mb-0">
                  {{ prompt.title }}
                </h6>
              </div>
              <div class="col-auto px-0 d-flex flex-row align-items-center">
                <span class="badge badge-pill badge-info">ORGANIZATION</span>
                <div class="col px-1 table-hover cursor-pointer">
                  <div class="dropdown">
                    <button
                      class="btn btn-sm p-0"
                      type="button"
                      id="dropdownMenuButton{{ prompt.id }}"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                    >
                      <img
                        src="{{
                          assetBasePath
                        }}Icon/prompt-vault/DotsThreeOutlineVertical.svg"
                      />
                    </button>
                    <div
                      class="dropdown-menu px-1 body1 dropdown-menu-right py-0"
                      [attr.aria-labelledby]="'dropdownMenuButton' + prompt.id"
                    >
                      <div class="dropdown-item px-1 cursor-pointer">
                        <div
                          class="row mx-0 align-items-center flex-nowrap"
                          (click)="
                            toChatPage('New Chat'); executePrompt(prompt.title)
                          "
                        >
                          <img
                            src="{{
                              assetBasePath
                            }}Icon/prompt-vault/NavigationArrow.svg"
                            width="24px"
                            height="24px"
                          />
                          <div class="px-1">Execute</div>
                        </div>
                      </div>
                      <div class="dropdown-item px-1 cursor-pointer">
                        <div
                          class="row mx-0 align-items-center flex-nowrap"
                          data-toggle="modal"
                          data-target="#exampleModal"
                          (click)="startEdit(prompt.id)"
                        >
                          <img
                            src="{{
                              assetBasePath
                            }}Icon/prompt-vault/NotePencil.svg"
                            width="24px"
                            height="24px"
                            class="grayscale"
                          />
                          <div class="px-1">Add Description</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="d-flex flex-column">
              <span
                class="small px-2"
                *ngIf="
                  prompt.desc && prompt.desc !== 'No description available'
                "
                data-toggle="modal"
                data-target="#exampleModal"
                (click)="startEdit(prompt.id); isReadVew(true)"
                >READ MORE</span
              >
              <span *ngIf="prompt.timeAgo" class="small text-inactive px-2">{{
                prompt.timeAgo
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    *ngIf="
      viewAllState['personal'] ||
      (!viewAllState['global_alias'] &&
        !viewAllState['personal'] &&
        !viewAllState['shared'])
    "
    class="mb-3"
  >
    <div class="d-flex flex-row justify-content-between">
      <h6 class="mb-0">MY PROMPTS</h6>
      <h6
        class="mb-0 cursor-pointer"
        *ngIf="globalPromptTotal >= 2"
        (click)="togglePromptLimit('personal'); selectedPromptType('personal')"
      >
        {{ viewAllState["personal"] ? "View Less" : "View All" }}
      </h6>
    </div>
    <hr />

    <div
      class="d-flex flex-colum align-items-center py-2 px-2"
      *ngIf="personalPromptTotal == 0"
    >
      <div
        class="col justify-content-center text-center align-items-center flex-fill"
      >
        <div class="text-center">
          <h6>No Personal Prompts to Show</h6>
        </div>
      </div>
    </div>
    <div *ngFor="let prompt of promptss; let i = index">
      <div class="pb-2" *ngIf="prompt.type == 'personal'">
        <div class="card flex-fill border-0 shadow1">
          <div class="card-body p-2">
            <div class="d-flex flex-row align-items-center">
              <div class="col px-2 mb-0 body2 ellipsis text-nowrap">
                <h6 class="ellipsis mb-0">
                  {{ prompt.title }}
                </h6>
              </div>
              <div class="col-auto px-0 d-flex flex-row align-items-center">
                <span class="badge badge-pill badge-success">MY PROMPT</span>
                <div class="col px-1 table-hover cursor-pointer">
                  <div class="dropdown">
                    <button
                      class="btn btn-sm p-0"
                      type="button"
                      id="dropdownMenuButton{{ prompt.id }}"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                    >
                      <img
                        src="{{
                          assetBasePath
                        }}Icon/prompt-vault/DotsThreeOutlineVertical.svg"
                      />
                    </button>
                    <div
                      class="dropdown-menu px-1 body1 dropdown-menu-right py-0"
                      [attr.aria-labelledby]="'dropdownMenuButton' + prompt.id"
                    >
                      <div class="dropdown-item px-1 cursor-pointer">
                        <div
                          class="row mx-0 align-items-center flex-nowrap"
                          (click)="
                            toChatPage('New Chat'); executePrompt(prompt.title)
                          "
                        >
                          <img
                            src="{{
                              assetBasePath
                            }}Icon/prompt-vault/NavigationArrow.svg"
                            width="24px"
                            height="24px"
                          />
                          <div class="px-1">Execute</div>
                        </div>
                      </div>
                      <div class="dropdown-item px-1 cursor-pointer">
                        <div
                          class="row mx-0 align-items-center flex-nowrap"
                          data-toggle="modal"
                          data-target="#exampleModal"
                          (click)="startEdit(prompt.id); isReadVew(false)"
                        >
                          <img
                            src="{{
                              assetBasePath
                            }}Icon/prompt-vault/NotePencil.svg"
                            width="24px"
                            height="24px"
                          />
                          <div class="px-1">Add Description</div>
                        </div>
                      </div>
                      <div class="dropdown-item px-1 cursor-pointer">
                        <div
                          class="row mx-0 align-items-center flex-nowrap"
                          data-toggle="modal"
                          data-target="#sharePromptModal"
                        >
                          <img
                            src="{{
                              assetBasePath
                            }}Icon/prompt-vault/ShareNetwork.svg"
                            width="24px"
                            height="24px"
                          />
                          <div class="px-1">Share</div>
                        </div>
                      </div>
                      <div
                        class="dropdown-item px-1 cursor-pointer"
                        (click)="deletePromptAPI(prompt.id)"
                      >
                        <div class="row mx-0 align-items-center flex-nowrap">
                          <img src="{{ assetBasePath }}Icon/chat/Trash.svg" />
                          <div class="px-1">Remove from Vault</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="d-flex flex-column">
              <span
                class="small px-2"
                *ngIf="
                  prompt.desc && prompt.desc !== 'No description available'
                "
                data-toggle="modal"
                data-target="#exampleModal"
                (click)="startEdit(prompt.id); isReadVew(true)"
                >READ MORE</span
              >
              <span *ngIf="prompt.timeAgo" class="small text-inactive px-2">{{
                prompt.timeAgo
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <!-- <div class="d-flex justify-content-between mt-3">
    <button
      class="btn btn-secondary"
      [disabled]="currentSkip === 0"
      (click)="previousPage()"
    >
      Previous
    </button>
    <button
      class="btn btn-secondary"
      [disabled]="currentSkip + currentLimit >= totalPrompts"
      (click)="nextPage()"
    >
      Next
    </button>
  </div> -->
  </div>

  <!-- Shared-->

  <div
    *ngIf="
      viewAllState['shared'] ||
      (!viewAllState['global_alias'] &&
        !viewAllState['personal'] &&
        !viewAllState['shared'])
    "
    class="mb-3"
  >
    <div class="d-flex flex-row justify-content-between">
      <h6 class="mb-0">SHARED</h6>
      <h6
        class="mb-0 cursor-pointer"
        *ngIf="globalPromptTotal >= 2"
        (click)="togglePromptLimit('shared'); selectedPromptType('shared')"
      >
        {{ viewAllState["shared"] ? "View Less" : "View All" }}
      </h6>
    </div>
    <hr />
    <div
      class="d-flex flex-colum align-items-center py-2 px-2"
      *ngIf="sharedPromptTotal == 0"
    >
      <div
        class="col justify-content-center text-center align-items-center flex-fill"
      >
        <div class="text-center">
          <h6>No Shared Prompts to show</h6>
        </div>
      </div>
    </div>
    <div *ngFor="let prompt of promptss; let i = index">
      <div class="pb-2" *ngIf="prompt.type == 'shared'">
        <div class="card flex-fill border-0 shadow1">
          <div class="card-body p-2">
            <div class="d-flex flex-row align-items-center">
              <div class="col px-2 mb-0 body2 ellipsis text-nowrap">
                <h6 class="ellipsis mb-0">
                  {{ prompt.title }}
                </h6>
              </div>
              <div class="col-auto px-0 d-flex flex-row align-items-center">
                <span class="badge badge-pill badge-dark">{{
                  prompt.type
                }}</span>
                <div class="col px-1 table-hover cursor-pointer">
                  <div class="dropdown">
                    <button
                      class="btn btn-sm p-0"
                      type="button"
                      id="dropdownMenuButton{{ prompt.id }}"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                    >
                      <img
                        src="{{
                          assetBasePath
                        }}Icon/prompt-vault/DotsThreeOutlineVertical.svg"
                      />
                    </button>
                    <div
                      class="dropdown-menu px-1 body1 dropdown-menu-right py-0"
                      [attr.aria-labelledby]="'dropdownMenuButton' + prompt.id"
                    >
                      <div class="dropdown-item px-1 cursor-pointer">
                        <div
                          class="row mx-0 align-items-center flex-nowrap"
                          (click)="
                            toChatPage('New Chat'); executePrompt(prompt.title)
                          "
                        >
                          <img
                            src="{{
                              assetBasePath
                            }}Icon/prompt-vault/NavigationArrow.svg"
                            width="24px"
                            height="24px"
                          />
                          <div class="px-1">Execute</div>
                        </div>
                      </div>
                      <div class="dropdown-item disabled px-1 cursor-pointer">
                        <div
                          class="row mx-0 align-items-center flex-nowrap"
                          data-toggle="modal"
                          data-target="#exampleModal"
                          (click)="startEdit(prompt.id)"
                        >
                          <img
                            src="{{
                              assetBasePath
                            }}Icon/prompt-vault/NotePencil.svg"
                            width="24px"
                            height="24px"
                          />
                          <div class="px-1">Add Description</div>
                        </div>
                      </div>
                      <div
                        class="dropdown-item px-1 cursor-pointer"
                        (click)="deletePromptAPI(prompt.id)"
                      >
                        <div class="row mx-0 align-items-center flex-nowrap">
                          <img src="{{ assetBasePath }}Icon/chat/Trash.svg" />
                          <div class="px-1">Remove from Vault</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="d-flex flex-column">
              <span class="small px-2">READ MORE</span>
              <span *ngIf="prompt.timeAgo" class="small text-inactive px-2">{{
                prompt.timeAgo
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal Add Description-->
<div
  class="modal fade"
  id="exampleModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="exampleModalLabel"
>
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-primary">
        <h5
          class="modal-title text-white"
          id="exampleModalLabel"
          *ngIf="isDecView == true"
        >
          Description
        </h5>
        <h5
          class="modal-title text-white"
          id="exampleModalLabel"
          *ngIf="isDecView == false"
        >
          Edit Description
        </h5>
        <button
          type="button"
          class="close text-white"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="pb-4">
          {{ editedDec }}
        </div>
        <div class="pt-2" *ngIf="!isDecView">
          <input
            class="form-control bg-"
            type="text"
            placeholder="Add Description to Your Prompt"
            [(ngModel)]="inputDec"
          />
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-sm btn-outline-primary"
          data-dismiss="modal"
        >
          Close
        </button>
        <button
          *ngIf="isDecView == false"
          type="button"
          class="btn btn-primary btn-sm"
          data-dismiss="modal"
          (click)="saveEdit()"
        >
          Save
        </button>
        <button
          type="button"
          class="btn btn-primary btn-sm"
          (click)="isReadVew(false)"
          *ngIf="isDecView == true"
        >
          Edit
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Share Prompts-->
<div
  class="modal fade"
  id="sharePromptModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="sharePromptModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-primary">
        <h5 class="modal-title text-white" id="sharePromptModalLabel">
          Share Prompts
        </h5>
        <button
          type="button"
          class="close text-white"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div *ngIf="successMessage" class="alert alert-success mt-2">
          {{ successMessage }}
        </div>
        <div>
          <div *ngIf="!shareCompleted">
            <div class="d-flex flex-row justify-content-between">
              <h6 class="mb-0">Add People and Teams</h6>
            </div>

            <ng-select
              [items]="options"
              [multiple]="true"
              bindLabel="name"
              [selectableGroup]="true"
              [searchable]="true"
              groupBy="team"
              [(ngModel)]="selectedOptions"
            >
              <ng-template ng-option-tmp let-item="item">
                <div class="d-flex align-items-center">
                  <img
                    *ngIf="item.avatar; else placeholder"
                    [src]="item.avatar"
                    alt="{{ item.name }}"
                    class="rounded-circle"
                    width="30"
                    height="30"
                  />
                  <ng-template #placeholder>
                    <div
                      class="initials"
                      style="
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        background-color: #ccc;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      "
                    >
                      {{ item.name.charAt(0) }}
                    </div>
                  </ng-template>
                  <div class="ml-2">
                    <strong>{{ item.name }}</strong
                    ><br />
                    <small>{{ item.email }}</small>
                  </div>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <div *ngIf="selectedOptions.length > 0">
            <div class="d-flex justify-content-between pt-2">
              <h6>Selected Individuals</h6>
              <h6>{{ selectedOptions.length }}</h6>
            </div>
            <div class="card pt-2 rounded-lg">
              <div>
                <div class="d-flex flex-wrap">
                  <div
                    *ngFor="let option of selectedOptions; let i = index"
                    class="d-flex align-items-center col-6 px-1 mb-2"
                  >
                    <div class="d-flex flex-row px-2">
                      <img
                        *ngIf="option.avatar; else placeholder"
                        [src]="option.avatar"
                        class="rounded"
                        alt="{{ option.name }}"
                        width="40px"
                        height="40px"
                      />
                      <ng-template #placeholder>
                        <div
                          class="initials"
                          style="
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background-color: #ccc;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                          "
                        >
                          {{ option.name.charAt(0) }}
                        </div>
                      </ng-template>
                      <div class="text-black pl-3">
                        <h6 class="text-black mb-0">{{ option.name }}</h6>
                        <div class="subtitle2">{{ option.email }}</div>
                      </div>
                    </div>

                    <!-- Create a new row after every second item -->
                    <div *ngIf="(i + 1) % 2 === 0" class="w-100 col-12"></div>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="getUniqueTeams().length > 0">
              <div class="d-flex justify-content-between pt-2">
                <h6>Selected Teams</h6>
                <h6>{{ getUniqueTeams().length }}</h6>
              </div>
              <div class="card pt-2 rounded-lg">
                <div class="d-flex flex-wrap">
                  <div
                    *ngFor="let team of getUniqueTeams(); let i = index"
                    class="d-flex align-items-center col-6 mb-2"
                  >
                    <div class="d-flex align-items-center">
                      <div
                        class="initials"
                        style="
                          width: 40px;
                          height: 40px;
                          border-radius: 50%;
                          background-color: #ccc;
                          display: flex;
                          align-items: center;
                          justify-content: center;
                        "
                      >
                        {{ team.charAt(0) }}
                      </div>

                      <div class="text-black pl-3">
                        <h6 class="text-black mb-0">{{ team }}</h6>
                      </div>
                    </div>
                    <div *ngIf="(i + 1) % 2 === 0" class="w-100 col-12"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          *ngIf="!shareCompleted"
          type="button"
          class="btn btn-sm btn-outline-primary"
          data-dismiss="modal"
        >
          Close
        </button>
        <button
          *ngIf="!shareCompleted"
          type="button"
          class="btn btn-primary btn-sm"
          (click)="sharePrompts()"
        >
          Share
        </button>
        <button
          *ngIf="shareCompleted"
          type="button"
          class="btn btn-sm btn-primary"
          data-dismiss="modal"
        >
          Done
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Add Organisation Prompt-->
<div
  class="modal fade"
  id="orgPrompt"
  tabindex="-1"
  role="dialog"
  aria-labelledby="orgPromptLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-primary">
        <h5 class="modal-title text-white" id="orgPromptLabel">
          Add Organization Prompt
        </h5>
        <button
          type="button"
          class="close text-white"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <app-alert style="width: 100%"></app-alert>
        <div class="pb-4">
          <div class="py-2">
            Enter Prompt
            <button
              type="button"
              class="btn btn-sm btn-outline-primary ml-2"
              (click)="ClearPromptsInputs()"
            >
              Create New Prompt
            </button>
          </div>

          <div class="">
            <input
              class="form-control bg-"
              type="text"
              placeholder="Enter Your Prompt"
              [(ngModel)]="newPromptTitle"
            />
          </div>
        </div>
        <div class="pb-4">
          <div class="py-2">Enter Description</div>
          <div class="">
            <textarea
              class="form-control"
              placeholder="Enter Prompt Description"
              [(ngModel)]="newPromptDescription"
            ></textarea>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-sm btn-outline-primary"
          data-dismiss="modal"
        >
          Close
        </button>
        <button
          type="button"
          class="btn btn-primary btn-sm"
          (click)="
            onAddPrompt(1, 'global_alias', newPromptTitle, newPromptDescription)
          "
        >
          Add
        </button>
      </div>
    </div>
  </div>
</div>
