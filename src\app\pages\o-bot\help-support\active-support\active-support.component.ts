import { HttpClient } from '@angular/common/http';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { AlertService } from 'src/app/services/alert/alert.service';
import { HelpSupportService } from 'src/app/services/help/help-support.service';
import { LogService } from 'src/app/services/log/log.service';
import { UserInfoService } from 'src/app/services/user-info/user-info.service';
import { environment } from 'src/environments/environment.development';

@Component({
  selector: 'app-active-support',
  templateUrl: './active-support.component.html',
  styleUrls: ['./active-support.component.css'],
})
export class ActiveSupportComponent {
  @Input() selectedFaq: string | null = null; // Input for selected FAQ
  @Output() feedbackGiven = new EventEmitter<boolean>();

  apiUrl = `${environment.apiUrl}/feedback/create`;

  feedback = {
    name: '',
    email: '',
    title: '',
    message: '',
  };

  isSubmitting = false;

  // Validation flags
  validationErrors = {
    email: false,
    title: false,
    message: false,
  };

  constructor(
    private helpSupportService: HelpSupportService,
    private logService: LogService,
    private userInfoService: UserInfoService,
    private http: HttpClient,
    private alertService: AlertService
  ) {}

  ngOnInit() {
    this.userInfoService.getUserInfo().subscribe((user) => {
      this.feedback.name = user.userName;
    });
  }

  onFeedback(isHelpful: boolean) {
    this.feedbackGiven.emit(isHelpful);
    this.helpSupportService.clearSelection();
  }
  onGoBack() {
    this.helpSupportService.clearSelection();
  }

  // Method to handle the feedback form submission
  onSubmit() {
    this.validateForm();
    if (this.isFormValid()) {
      this.isSubmitting = true;
      this.http
        .post(this.apiUrl, {
          name: this.feedback.name,
          email: this.feedback.email,
          title: this.feedback.title,
          message: this.feedback.message,
        })
        .subscribe({
          next: (response) => {
            this.logService.log('Feedback submitted successfully');
            this.alertService.showAlert(
              'Feedback submitted successfully',
              'success'
            );
            this.feedbackGiven.emit(true);
            this.resetForm();
            this.isSubmitting = false;
          },
          error: (error) => {
            this.logService.log('Error submitting feedback: ');
            this.alertService.showAlert('Error submitting feedback', 'error');
            this.isSubmitting = false;
          },
        });
    } else {
      this.logService.log('Please fill out all required fields');
      this.alertService.showAlert(
        'Please fill out all required fields',
        'warning'
      );
    }
  }

  validateForm() {
    this.validationErrors.email = !this.validateEmail(this.feedback.email);
    this.validationErrors.title = !this.feedback.title.trim();
    this.validationErrors.message = !this.feedback.message.trim();
  }

  // Optional: You can add a method to validate the form
  isFormValid(): boolean {
    return Object.values(this.validationErrors).every((err) => !err);
  }

  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Detect changes and remove validation error if input is corrected
  onInputChange(field: keyof typeof this.validationErrors) {
    if (field === 'email') {
      this.validationErrors.email = !this.validateEmail(this.feedback.email);
    } else {
      this.validationErrors[field] = !this.feedback[field].trim();
    }
  }

  resetForm() {
    this.feedback.title = '';
    this.feedback.message = '';
    this.feedback.email = '';
    this.validationErrors = { email: false, title: false, message: false };
  }
}
