<!-- <p>
  Fullscreen Mode: <strong>{{ isFullscreen ? "ON" : "OFF" }}</strong>
</p> -->
<div class="container-fluid">
  <div class="row px-3 d-flex justify-content-end">
    <div class="col">
      <img
        class="position-fixed"
        style="bottom: 0; right: 0; cursor: pointer"
        (click)="ObotWindow()"
        src="{{ assetBasePath }}O-BOT-logo.png"
      />
    </div>
  </div>
</div>

<div class="d-flex flex-row">
  <!-- Sidebar web-->
  <div class="py-2 px-2 bg-primary sidebar-web" *ngIf="fullScreenToggle">
    <div class="col px-0 justify-content-center">
      <div class="d-flex flex-row pb-3 align-items-center">
        <div
          *ngIf="!userPhoto"
          class="avatar rounded-circle d-flex justify-content-center align-items-center"
          [style.background]="avatarColor"
        >
          {{ initials }}
        </div>
        <div
          class="avatar rounded-circle d-flex justify-content-center align-items-center"
          *ngIf="userPhoto != null"
        >
          <img
            *ngIf="userPhoto"
            [src]="userPhoto"
            alt="User Photo"
            width="38"
            height="38"
            class="avatar rounded-circle d-flex justify-content-center align-items-center"
          />
        </div>
        <div class="text-white pl-2">
          <div>{{ userName }}</div>
          <!-- <div>{{ email }}</div> -->
        </div>
      </div>
      <div class="d-flex flex-row pb-2">
        <div
          class="btn bg-white d-flex flex-row container-fluid justify-content-center cursor-pointer"
          (click)="selectPage('New Chat', chatComp); setNewChat()"
        >
          <img src="{{ assetBasePath }}Icon/Plus.svg" class="pr-2" alt="..." />
          <div class="text-primary">New Chat</div>
        </div>
      </div>
      <div *ngFor="let link of links">
        <div class="d-flex flex-row pb-2" (click)="selectPage(link.pageName)">
          <div
            (mouseenter)="hoveredLinkId = link.id"
            (mouseleave)="hoveredLinkId = null"
            [ngClass]="link.isActive ? 'bg-primary-200' : ''"
            class="btn d-flex flex-row container-fluid text-white hover-bg-primary-200 cursor-pointer"
          >
            <img
              [src]="
                hoveredLinkId === link.id || link.isActive
                  ? link.filledIcon
                  : link.regularIcon
              "
              class="pr-2"
              alt="..."
            />
            <div class="">{{ link.pageName }}</div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="col px-0 justify-content-center flex-fill"
      style="bottom: 0; position: fixed; width: 194px"
    >
      <div
        class="d-flex flex-row pb-2"
        (mouseenter)="hoveredLinkId = setting.id"
        (mouseleave)="hoveredLinkId = null"
        (click)="selectPage(setting.pageName)"
      >
        <div
          [ngClass]="setting.isActive ? 'bg-primary-200' : ''"
          class="btn d-flex flex-row container-fluid text-white hover-bg-primary-200 cursor-pointer"
        >
          <img
            [src]="
              hoveredLinkId === pinned.id || setting.isActive
                ? setting.filledIcon
                : setting.regularIcon
            "
            class="pr-2"
            alt="..."
          />
          <div class="">{{ setting.pageName }}</div>
        </div>
      </div>
    </div>
  </div>

  <div
    class=" "
    *ngIf="obotWindowToggle"
    [ngClass]="
      this.fullScreenToggle == true
        ? 'o-bot-window-side full-screen'
        : 'o-bot-window-side'
    "
  >
    <!-- Header -->
    <!-- <div class="container-fluid width-100">
      <div class="row bg-primary justify-content-between">
        <div class="d-flex flex-row align-items-center py-2 px-2">
          <img src="{{ assetBasePath }}Logo-O-BOT-Small.png" />
          <h3 class="px-2 mb-0 text-white">AQuA Pro Agent</h3>
        </div>
        <div class="d-flex flex-row align-items-center py-2 px-2">
          <img
            class="cursor-pointer p-2"
            [src]="
              fullScreenToggle
                ? assetBasePath + 'Icon/ArrowsIn.svg'
                : assetBasePath + 'Icon/ArrowsOut.svg'
            "
            (click)="fullScreenView()"
          />

          <img
            class="cursor-pointer p-2"
            (click)="clearLocalStorage(); ObotWindow(); closeAgent()"
            src="{{ assetBasePath }}Icon/X.svg"
          />
        </div>
      </div>
    </div>
    <hr class="my-0" /> -->
    <!--Secondary Header -->
    <div class="container-fluid width-100">
      <div
        class="row bg-primary justify-content-between align-items-center px-2"
      >
        <div>
          <h6 class="px-0 mb-0 text-white" *ngIf="selectedPage != 'New Chat'">
            {{ selectedPage }}
          </h6>
          <div class="d-flex flex-row align-items-center">
            <h6
              class="px-0 mb-0 text-white"
              *ngIf="selectedPage == 'New Chat'"
              [ngClass]="fullScreenToggle ? 'ellipsis full-screen' : 'ellipsis'"
            >
              {{ testChatName }}
            </h6>
            <button
              class="btn btn-sm btn-primary ml-2"
              (click)="pinChat()"
              *ngIf="selectedPage == 'New Chat' && testChatName != 'New Chat'"
            >
              <img
                [src]="
                  pinned.isActive || hoveredLinkId === pinned.id
                    ? pinned.filledIcon
                    : pinned.regularIcon
                "
                height="20px"
                (mouseenter)="hoveredLinkId = pinned.id"
                (mouseleave)="hoveredLinkId = null"
              />
            </button>
          </div>
        </div>
        <div
          class="d-flex flex-row align-items-center px-2"
          style="min-height: 32px"
        >
          <img
            class="cursor-pointer p-1"
            (click)="sideBar()"
            *ngIf="!fullScreenToggle"
            src="{{ assetBasePath }}Icon/SidebarSimple.svg"
          />
        </div>
      </div>
    </div>

    <app-alert
      class="position-absolute"
      style="width: 100%; z-index: 1"
    ></app-alert>
    <!-- Sidebar -->
    <div
      class="py-2 px-2 bg-primary sidebar-small"
      [ngClass]="sideBarOpen ? 'slide-in' : 'slide-out'"
      style="z-index: 2"
    >
      <div class="col px-0 justify-content-center">
        <div class="d-flex flex-row pb-3 align-items-center">
          <div
            class="avatar rounded-circle d-flex justify-content-center align-items-center"
            [style.background]="avatarColor"
          >
            {{ initials }}
          </div>
          <div class="text-white pl-2">
            <div>{{ userName }}</div>
            <!-- <div>{{ email }}</div> -->
          </div>
        </div>
        <div class="d-flex flex-row pb-2">
          <div
            class="btn bg-white d-flex flex-row container-fluid justify-content-center cursor-pointer"
            (click)="selectPage('New Chat', chatComp); setNewChat()"
          >
            <img
              src="{{ assetBasePath }}Icon/Plus.svg"
              class="pr-2"
              alt="..."
            />
            <div class="text-primary">New Chat</div>
          </div>
        </div>
        <div *ngFor="let link of links">
          <div class="d-flex flex-row pb-2" (click)="selectPage(link.pageName)">
            <div
              (mouseenter)="hoveredLinkId = link.id"
              (mouseleave)="hoveredLinkId = null"
              class="btn d-flex flex-row container-fluid text-white hover-bg-primary-200 cursor-pointer"
              [ngClass]="link.isActive ? 'bg-primary-200' : ''"
            >
              <img
                [src]="
                  hoveredLinkId === link.id || link.isActive
                    ? link.filledIcon
                    : link.regularIcon
                "
                class="pr-2"
                alt="..."
              />
              <div class="">{{ link.pageName }}</div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="col px-0 justify-content-center"
        style="bottom: 0; position: fixed; width: 94%"
      >
        <div
          class="d-flex flex-row pb-2"
          (mouseenter)="hoveredLinkId = setting.id"
          (mouseleave)="hoveredLinkId = null"
          (click)="selectPage(setting.pageName)"
        >
          <div
            class="btn d-flex flex-row container-fluid text-white hover-bg-primary-200 cursor-pointer"
            [ngClass]="setting.isActive ? 'bg-primary-200' : ''"
          >
            <img
              [src]="
                hoveredLinkId === setting.id || setting.isActive
                  ? setting.filledIcon
                  : setting.regularIcon
              "
              class="pr-2"
              alt="..."
            />
            <div class="">{{ setting.pageName }}</div>
          </div>
        </div>
      </div>
    </div>
    <hr class="my-0" />

    <!-- Content Window -->
    <ng-container *ngIf="loading; else contentTemplate">
      <div
        class="d-flex flex-row align-items-center py-2 px-2 bg-primary-800 bg-o-bot"
      >
        <div class="col justify-content-center text-center">
          <div class="text-center">
            <img src="{{ assetBasePath }}O-BOT-logo.png" />
            <p>Loading...</p>
          </div>
        </div>
      </div>
    </ng-container>

    <ng-template #contentTemplate>
      <div
        class="d-flex flex-row bg-primary-800 bg-o-bot justify-content-center position-relative"
      >
        <div
          class="col px-0"
          [ngClass]="fullScreenToggle ? 'max-width-90' : ''"
        >
          <ng-container [ngSwitch]="selectedPage">
            <div class="px-2 py-2">
              <app-chat
                #chatComp
                *ngSwitchCase="'New Chat'"
                [fullScreenToggle]="fullScreenToggle"
                [externalInput]="externalText"
                [buttonNewChat]="buttonNewChat"
              ></app-chat>
              <app-notifications
                *ngSwitchCase="'Notifications'"
              ></app-notifications>
              <app-prompt-vault
                *ngSwitchCase="'Prompt Vault'"
              ></app-prompt-vault>
              <app-pinned-items
                *ngSwitchCase="'Pinned Items'"
              ></app-pinned-items>
              <app-recent-chat *ngSwitchCase="'Recent Chats'"></app-recent-chat>
              <app-help-support *ngSwitchCase="'Support'"></app-help-support>
              <app-user-settings
                *ngSwitchCase="'User Settings'"
              ></app-user-settings>
              <div *ngSwitchDefault>Select a page from the sidebar.</div>
            </div>
          </ng-container>
        </div>
      </div>
    </ng-template>
  </div>
</div>
