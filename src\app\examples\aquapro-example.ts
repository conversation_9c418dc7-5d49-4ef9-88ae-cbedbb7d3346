import { Component } from '@angular/core';
import { ChatService } from '../services/chat/chat.service';
import { AquaProIntegrationResult } from '../interfaces/aquapro-response.interface';
import {
  parseAquaProResponse,
  isAquaProIntegrationSuccessful,
} from '../utils/aquapro-utils';
import { LogService } from '../services/log/log.service';
import { AlertService } from '../services/alert/alert.service';

/**
 * Example component showing how to use the AquaPro Integration API
 * This is just an example and not meant to be used directly
 */
@Component({
  selector: 'app-aquapro-example',
  template: `
    <div>
      <h2>AquaPro Integration Example</h2>
      <button (click)="createAquaProData()" [disabled]="isLoading">
        {{ isLoading ? 'Processing...' : 'Create AquaPro Data' }}
      </button>

      <div *ngIf="results.length > 0">
        <h3>Results:</h3>
        <ul>
          <li *ngFor="let result of results">
            {{ result.operation }}: {{ result.message }} ({{ result.status }})
          </li>
        </ul>
      </div>
    </div>
  `,
})
export class AquaProExampleComponent {
  isLoading = false;
  results: AquaProIntegrationResult[] = [];

  constructor(
    private chatService: ChatService,
    private logService: LogService,
    private alertService: AlertService
  ) {}

  /**
   * Example method to create AquaPro data
   * In a real component, you would get the messageId from the current context
   */
  createAquaProData() {
    // In a real component, you would get this from the current message
    const messageId = 'example-message-id';

    this.isLoading = true;
    this.results = [];

    this.chatService.createAquaProData(messageId).subscribe({
      next: (response) => {
        this.logService.log('AquaPro data created successfully');

        // Parse the response
        const results = parseAquaProResponse(response);
        this.results = results;

        // Check if all operations were successful
        const isSuccessful = isAquaProIntegrationSuccessful(results);

        // Show appropriate alert
        if (isSuccessful) {
          this.alertService.showAlert(
            'AquaPro data created successfully',
            'success'
          );
        } else {
          this.alertService.showAlert(
            'Some AquaPro operations failed',
            'warning'
          );
        }

        this.isLoading = false;
      },
      error: (error) => {
        this.alertService.showAlert('Failed to create AquaPro data', 'error');
        this.isLoading = false;
      },
    });
  }
}
