/* You can add global styles to this file, and also import other style files */

/* Import Open Sans from Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap");
@import "~@ng-select/ng-select/themes/default.theme.css";

:root {
  /* Primary Colors */
  --primary: #3abaab !important;
  --primary-100: #4cbfb1 !important;
  --primary-200: #61c8bc !important;
  --primary-300: #75cfc4 !important;
  --primary-400: #89d6cd !important;
  --primary-500: #9cdcd5 !important;
  --primary-600: #b0e3dd !important;
  --primary-700: #c4eae6 !important;
  --primary-800: #ebf8f7 !important;

  /* Black Shades */
  --black: #333333 !important;
  --black-100: #666666 !important;
  --black-200: #808080 !important;
  --black-300: #999999 !important;
  --black-400: #cfcfcf !important;
  --black-500: #e5e6e7 !important;
  --black-600: #efefef !important;
  --black-700: #f5f5f5 !important;

  /* Additional Colors */
  --white: #ffffff !important;
  --warning: #ffde17 !important;
  --positive: #61ae24 !important;
  --negative: #ef4136 !important;

  /* Text and Border Colors */
  --title-text: #333333 !important;
  --secondary-text: #666666 !important;
  --inactive: #999999 !important;
  --text-field: #cfcfcf !important;
  --divider-border: #e5e6e7 !important;
  --disabled-background: #f9f9fa !important;
}

body {
  font-family: "Open Sans", sans-serif;
  color: #333333 !important;
}

/* Headings */
h1 {
  font-weight: 300;
  font-size: 44px;
  line-height: 54px;
  letter-spacing: 0.2px;
}

h2 {
  font-weight: 500;
  font-size: 32px;
  line-height: 40px;
  letter-spacing: 0px;
}

h3 {
  font-weight: 600;
  font-size: 24px;
  line-height: 30px;
  letter-spacing: 0.1px;
}

h4 {
  font-weight: 500;
  font-size: 20px;
  line-height: 26px;
  letter-spacing: 0.2px;
}

h5 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: 0.2px;
}

h6 {
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0.2px;
}

/* Subtitles */
.subtitle1 {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.1px;
}

.subtitle2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  letter-spacing: 0.1px;
}

/* Body Text */
.body1 {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.1px;
}

.body2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0.1px;
}

/* Small Text */
.small1 {
  font-weight: 500;
  font-size: 13px;
  line-height: 16px;
  letter-spacing: 0.2px;
}

.small2 {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.2px;
}

.small3 {
  font-weight: 400;
  font-size: 11px;
  line-height: 14px;
  letter-spacing: 0.2px;
}
.small4 {
  font-weight: 400;
  font-size: 10px;
  letter-spacing: 0.2px;
}

/* Button Text */
.button {
  font-weight: 600;
  font-size: 14px;
  line-height: 18px;
  letter-spacing: 0px;
}

/* Initials */
.initials {
  font-weight: 600;
  font-size: 13px;
  line-height: 16px;
  letter-spacing: 0.2px;
}

/* Background Colors */
.bg-primary {
  background-color: var(--primary);
}
.bg-primary-100 {
  background-color: var(--primary-100);
}
.bg-primary-200 {
  background-color: var(--primary-200);
}
.bg-primary-300 {
  background-color: var(--primary-300);
}
.bg-primary-400 {
  background-color: var(--primary-400);
}
.bg-primary-500 {
  background-color: var(--primary-500);
}
.bg-primary-600 {
  background-color: var(--primary-600);
}
.bg-primary-700 {
  background-color: var(--primary-700);
}
.bg-primary-800 {
  background-color: var(--primary-800);
}

/* Text Colors */
.text-primary {
  color: var(--primary) !important;
}
.text-primary-100 {
  color: var(--primary-100);
}
.text-primary-200 {
  color: var(--primary-200);
}
.text-primary-300 {
  color: var(--primary-300);
}
.text-primary-400 {
  color: var(--primary-400);
}
.text-primary-500 {
  color: var(--primary-500);
}
.text-primary-600 {
  color: var(--primary-600);
}
.text-primary-700 {
  color: var(--primary-700);
}
.text-primary-800 {
  color: var(--primary-800);
}

.text-black {
  color: var(--title-text) !important;
}

.text-secondary {
  color: var(--secondary-text) !important;
}

.text-inactive {
  color: var(--inactive) !important;
}

.text-field {
  color: var(--text-field) !important;
}

.text-divider {
  color: var(--divider-border) !important;
}

/* Border Colors */

.border-primary {
  border: 1px solid var(--primary) !important;
}
.border-primary-100 {
  border: 1px solid var(--primary-100);
}
.border-primary-200 {
  border: 1px solid var(--primary-200);
}
.border-primary-300 {
  border: 1px solid var(--primary-300);
}
.border-primary-400 {
  border: 1px solid var(--primary-400);
}
.border-primary-500 {
  border: 1px solid var(--primary-500);
}
.border-primary-600 {
  border: 1px solid var(--primary-600);
}
.border-primary-700 {
  border: 1px solid var(--primary-700);
}
.border-primary-800 {
  border: 1px solid var(--primary-800);
}

/* Hover Background Colors */
.hover-bg-primary:hover {
  background-color: var(--primary);
}
.hover-bg-primary-100:hover {
  background-color: var(--primary-100);
}
.hover-bg-primary-200:hover {
  background-color: var(--primary-200);
}
.hover-bg-primary-300:hover {
  background-color: var(--primary-300);
}
.hover-bg-primary-400:hover {
  background-color: var(--primary-400);
}
.hover-bg-primary-500:hover {
  background-color: var(--primary-500);
}
.hover-bg-primary-600:hover {
  background-color: var(--primary-600);
}
.hover-bg-primary-700:hover {
  background-color: var(--primary-700);
}
.hover-bg-primary-800:hover {
  background-color: var(--primary-800);
}

/* Hover Text Colors */
.hover-text-primary:hover {
  color: var(--primary);
}
.hover-text-primary-100:hover {
  color: var(--primary-100);
}
.hover-text-primary-200:hover {
  color: var(--primary-200);
}
.hover-text-primary-300:hover {
  color: var(--primary-300);
}
.hover-text-primary-400:hover {
  color: var(--primary-400);
}
.hover-text-primary-500:hover {
  color: var(--primary-500);
}
.hover-text-primary-600:hover {
  color: var(--primary-600);
}
.hover-text-primary-700:hover {
  color: var(--primary-700);
}
.hover-text-primary-800:hover {
  color: var(--primary-800);
}

.btn {
  transition: box-shadow 0.3s ease;
}
.btn:hover {
  box-shadow: 0 1px 4px rgba(12, 12, 13, 0.1);
}

.hover {
  transition: box-shadow 0.3s ease;
}
.hover:hover {
  box-shadow: 0 1px 4px rgba(12, 12, 13, 0.1);
}

.btn-primary {
  color: #fff;
  background-color: #3abaab;
  border-color: #3abaab;
}

.text-primary {
  color: #3abaab !important;
}

.dark-blue {
  color: #113f8c; /* Text color */
}

.width-100 {
  width: 100%;
}

.bg-primary {
  background-color: #3abaab !important;
}
.btn-dark-blue {
  color: #fff; /* Text color */
  background-color: #113f8c;
}
.btn .btn-dark-blue:hover {
  color: #fff;
  text-decoration: none;
}
.btn-outline-primary {
  color: #3abaab;
  border-color: #3abaab;
}
hr {
  border-top: 1px solid #e5e6e7;
}

.bg-primary-700 {
  background-color: #d8f1ee !important;
}
.bg-primary-800 {
  background-color: #ebf8f7 !important;
}
.cursor-pointer {
  cursor: pointer;
}

.badge-primary {
  color: #fff;
  background-color: #3abaab;
}
.badge {
  font-weight: 500;
}
.shadow1 {
  box-shadow: 0px 4px 20px rgba(58, 186, 171, 0.2); /* X, Y, Blur, Color with opacity */
}
.shadow-lg {
  box-shadow: 0 16px 32px -4px rgba(12, 12, 13, 0.1);
}
.ellipsis {
  white-space: nowrap; /* Prevents the text from wrapping */
  overflow: hidden; /* Hides any overflow text */
  text-overflow: ellipsis; /* Adds ellipsis (...) for overflow text */
}
.initials-circle {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0e0e0;
  color: #757575;
  font-weight: bold;
  border-radius: 50%;
  font-size: 16px;
}
.display-hover {
  display: none;
}
.display-hover:hover {
  display: 1;
}
.input-query {
  position: fixed;
  bottom: 40px;
  transform: translateX(-50%);
  z-index: 1;
  width: calc(100% - 74%);
  left: calc(100% - 14%);
}
.input-query.fullscreen-view {
  position: fixed;
  bottom: 28px;
  left: calc(100% - (100% - 168px) / 2); /* Center horizontally */
  transform: translateX(-48.5%); /* Center offset adjustment */
  z-index: 1;
  max-width: 74%;
}
