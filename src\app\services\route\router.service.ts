import { APP_INITIALIZER, Injectable } from '@angular/core';
import { Event, NavigationEnd, Router } from '@angular/router';
import { Subject, Subscription } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class RouterService {
  private navigationEndSubject: Subject<string> = new Subject<string>();

  private navigationEndSubscription: Subscription = new Subscription();
  constructor(private router: Router) {}

  initialize() {
    this.subscribeToNavigationEnd();
  }

  private subscribeToNavigationEnd() {
    this.navigationEndSubscription = this.router.events.subscribe(
      (event: Event) => {
        if (event instanceof NavigationEnd) {
          this.navigationEndSubject.next(event.url);
          // this.handleNavigationEnd(event.url);
        }
      }
    );
  }

  getNavigationEndObservable() {
    return this.navigationEndSubject.asObservable();
  }

  // using it for only app.component.ts
  getNavEndObservable() {
    return this.navigationEndSubject.asObservable();
  }

  ngOnDestroy() {
    if (this.navigationEndSubscription) {
      this.navigationEndSubscription.unsubscribe();
    }
  }
}

export const routerServiceProvider = {
  provide: APP_INITIALIZER,
  useFactory: (routerService: RouterService) => () =>
    routerService.initialize(),
  deps: [RouterService],
  multi: true,
};
