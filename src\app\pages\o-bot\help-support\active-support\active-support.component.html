<div *ngIf="!selectedFaq">
  <h4>Submit Feedback</h4>
  <div>
    <div>
      <div class="col px-0 d-flex pb-2">
        <div class="card flex-fill border-0 shadow1">
          <div class="card-body p-2">
            <form (ngSubmit)="onSubmit()">
              <div class="form-group">
                <label for="name">Name</label>
                <input
                  type="text"
                  class="form-control"
                  id="name"
                  placeholder="Your Name"
                  [(ngModel)]="feedback.name"
                  name="name"
                  readonly
                  required
                />
              </div>
              <div class="form-group">
                <label for="email">Email</label>
                <input
                  type="email"
                  class="form-control"
                  id="email"
                  placeholder="Email"
                  [(ngModel)]="feedback.email"
                  name="email"
                  [disabled]="isSubmitting"
                  [ngClass]="{ 'is-invalid': validationErrors.email }"
                  (ngModelChange)="onInputChange('email')"
                  required
                />
                <div class="invalid-feedback" *ngIf="validationErrors.email">
                  Please enter a valid email address.
                </div>
              </div>
              <div class="form-group">
                <label for="title">Feedback Title</label>
                <input
                  type="text"
                  class="form-control"
                  id="title"
                  placeholder="Feedback Title"
                  [(ngModel)]="feedback.title"
                  name="title"
                  [disabled]="isSubmitting"
                  [ngClass]="{ 'is-invalid': validationErrors.title }"
                  (ngModelChange)="onInputChange('title')"
                  required
                />
                <div class="invalid-feedback" *ngIf="validationErrors.title">
                  Feedback title is required.
                </div>
              </div>

              <div class="form-group">
                <label for="message">Feedback Message</label>
                <textarea
                  class="form-control"
                  id="message"
                  rows="3"
                  placeholder="Feedback Message"
                  [(ngModel)]="feedback.message"
                  name="message"
                  [disabled]="isSubmitting"
                  [ngClass]="{ 'is-invalid': validationErrors.message }"
                  (ngModelChange)="onInputChange('message')"
                  required
                ></textarea>
                <div class="invalid-feedback" *ngIf="validationErrors.message">
                  Feedback message is required.
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="d-flex flex-row py-3">
    <div>
      <button
        class="btn btn-outline-primary"
        style="width: 120px"
        (click)="onFeedback(true)"
      >
        Go Back
      </button>
    </div>
    <div class="col">
      <button
        class="btn btn-primary"
        style="width: 120px"
        type="submit"
        (click)="onSubmit()"
        [disabled]="isSubmitting"
      >
        {{ isSubmitting ? "Sending..." : "Send" }}
      </button>
    </div>
  </div>
</div>
