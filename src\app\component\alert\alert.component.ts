import { Component } from '@angular/core';
import { Subscription } from 'rxjs';
import { AlertService, AlertType } from 'src/app/services/alert/alert.service';

@Component({
  selector: 'app-alert',
  templateUrl: './alert.component.html',
  styleUrls: ['./alert.component.css'],
})
export class AlertComponent {
  message: string = '';
  color: string = 'green'; // Default color
  alertType: AlertType = 'info'; // Default type is 'info'

  private alertMessageSubscription?: Subscription;

  constructor(private alertService: AlertService) {}

  ngOnInit(): void {
    this.alertMessageSubscription = this.alertService.alertMessage$.subscribe(
      (data) => {
        this.message = data.message;
        this.alertType = data.type;
        this.color = this.color = this.getAlertColor(this.alertType);
        if (this.message) {
          setTimeout(() => {
            this.message = ''; // Clear message after 5 seconds
          }, data.duration || 3000);
        }
      }
    );
  }

  ngOnDestroy(): void {
    if (this.alertMessageSubscription) {
      this.alertMessageSubscription.unsubscribe();
    }
  }

  // Function to get alert color from service
  private getAlertColor(type: AlertType): string {
    return this.alertService['colorMapping'][type] || '#000000'; // Default to black if not found
  }
}
