h1 {
  font-weight: 300;
  font-size: 24px; /* Changed from 44px to 26px */
  line-height: 26px; /* Adjusted line height to match the new font size */
  letter-spacing: 0.2px; /* Keep the same letter-spacing */
}

.chat-container {
  height: -moz-calc(100% - (56px + 6px));
  height: -webkit-calc(100% - (56px + 6px));
  height: calc(100% - (56px + 6px));
  margin-bottom: 25%;
  max-height: calc(100vh - (270px));
  overflow-y: auto;
}

.chat-container.fullscreen-view {
  height: -moz-calc(100% - (56px + 6px));
  height: -webkit-calc(100% - (56px + 6px));
  height: calc(100% - (56px + 6px));
  margin-bottom: 32%;
  max-height: calc(100vh - (270px));
  overflow-y: auto;
}

.user-message {
  background-color: #e1ffc7; /* Light green for user messages */
  text-align: right; /* Align user messages to the right */
  width: 80%;
}

.bot-message {
  background-color: #f1f1f1; /* Light gray for bot messages */
  text-align: left; /* Align bot messages to the left */
}
.fade-in {
  animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
textarea {
  resize: none;
}

.table {
  table-layout: fixed;
  width: 100%;
}

.table td,
.table th {
  font-size: small;
  padding: 8px;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid #ddd;
  white-space: pre-line;
  word-wrap: break-word;
  overflow-wrap: break-word;
  text-align: left;
  vertical-align: top;
}

.table th {
  background-color: var(--bs-primary-200, #e3f2fd);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-container {
  max-width: 100%;
  overflow-x: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.excel-data-container {
  max-width: 100%; /* Adjust to your preferred container width */
  overflow-x: auto;
}

.card.user {
  max-width: fit-content; /* Limits the card width */
  overflow: hidden; /* Prevents content from overflowing the card */
}
.form-control:focus {
  box-shadow: none;
}
/* Loading Indicator Container */
.loading-indicator {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.loader {
  display: flex;
  gap: 4px; /* Space between dots */
}

.loader span {
  width: 6px; /* Dot size */
  height: 6px;
  background-color: var(--primary); /* Use your theme's primary color */
  border-radius: 50%; /* Make it circular */
  animation: dot-flash 1.4s infinite ease-in-out;
}

.loader span:nth-child(1) {
  animation-delay: 0s;
}
.loader span:nth-child(2) {
  animation-delay: 0.2s;
}
.loader span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-flash {
  0%,
  80%,
  100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}

.shimmer {
  font-size: 16px;
  color: #888;
  position: relative;
  overflow: hidden;
  display: inline-block;
  background: linear-gradient(
    90deg,
    var(--text-primary) 0%,
    var(--black-500) 50%,
    var(--text-primary) 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: shimmer 1.5s linear infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.stop-btn {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  background-color: #ff4b4b; /* Red color to signify stopping */
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stop-btn:hover {
  background-color: #ff1f1f; /* Slightly darker red on hover */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* Enhance shadow on hover */
}

.stop-btn:active {
  background-color: #e60000; /* Even darker red on click */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Reset shadow */
}

.stop-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 75, 75, 0.5); /* Red focus ring for accessibility */
}
