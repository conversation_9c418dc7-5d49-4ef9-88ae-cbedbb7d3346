h1 {
  font-weight: 300;
  font-size: 24px; /* Changed from 44px to 26px */
  line-height: 26px; /* Adjusted line height to match the new font size */
  letter-spacing: 0.2px; /* Keep the same letter-spacing */
}

.chat-container {
  height: -moz-calc(100% - (56px + 6px));
  height: -webkit-calc(100% - (56px + 6px));
  height: calc(100% - (56px + 6px));
  margin-bottom: 25%;
  max-height: calc(100vh - (270px));
  overflow-y: auto;
}

.chat-container.fullscreen-view {
  height: -moz-calc(100% - (56px + 6px));
  height: -webkit-calc(100% - (56px + 6px));
  height: calc(100% - (56px + 6px));
  margin-bottom: 32%;
  max-height: calc(100vh - (270px));
  overflow-y: auto;
}

.user-message {
  background-color: #e1ffc7; /* Light green for user messages */
  text-align: right; /* Align user messages to the right */
  width: 80%;
}

.bot-message {
  background-color: #f1f1f1; /* Light gray for bot messages */
  text-align: left; /* Align bot messages to the left */
}
.fade-in {
  animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
textarea {
  resize: none;
}

.table {
  table-layout: auto;
  width: auto;
  max-width: 100%;
}

.table td,
.table th {
  font-size: small;
  padding: 8px;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid #ddd;
  white-space: nowrap;
  text-align: left;
  vertical-align: top;
  width: auto;
}

.table td {
  white-space: pre-line;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 200px;
}

.table th {
  background-color: var(--bs-primary);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-container {
  max-width: 100%;
  overflow-x: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* Dynamic Table Styling */
.dynamic-table-container {
  max-width: 100%;
  overflow-x: auto;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background: white;
  margin: 10px 0;
}

.dynamic-table {
  table-layout: auto;
  width: 100%;
  margin-bottom: 0;
  font-size: 12px;
  border-collapse: separate;
  border-spacing: 0;
}

.dynamic-table .table-header-sticky {
  position: sticky;
  top: 0;
  z-index: 10;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
}

.dynamic-table .table-header {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white;
  font-weight: 600;
  font-size: 11px;
  padding: 12px 8px;
  border: 1px solid #357abd;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  position: relative;
  min-width: 80px;
  max-width: 200px;
}

.dynamic-table .header-content {
  line-height: 1.2;
  word-break: break-word;
}

.dynamic-table .table-cell {
  padding: 8px 6px;
  border: 1px solid #dee2e6;
  vertical-align: top;
  font-size: 11px;
  line-height: 1.3;
  min-width: 80px;
  max-width: 200px;
}

.dynamic-table .cell-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Merged cell styling */
.dynamic-table td[rowspan] {
  vertical-align: middle;
  border-right: 2px solid #357abd;
  background-color: #f8f9fa;
  position: relative;
}

.dynamic-table td[rowspan] .cell-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%;
  text-align: center;
  font-weight: 500;
}

/* Visual separator for merged sections */
.dynamic-table td[rowspan]:after {
  content: "";
  position: absolute;
  right: -1px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
}

/* Content-based cell styling */
.cell-empty {
  background-color: #f8f9fa;
  color: #6c757d;
  font-style: italic;
  text-align: center;
  min-width: 60px;
  max-width: 100px;
}

.cell-numeric {
  text-align: center;
  font-weight: 600;
  font-family: "Courier New", monospace;
  background-color: #fff3cd;
  min-width: 60px;
  max-width: 80px;
}

.cell-identifier {
  text-align: center;
  font-weight: 500;
  font-family: "Courier New", monospace;
  background-color: #d1ecf1;
  min-width: 80px;
  max-width: 120px;
}

.cell-text {
  text-align: left;
  min-width: 100px;
  max-width: 150px;
}

.cell-description {
  text-align: left;
  min-width: 180px;
  max-width: 300px;
}

.cell-multiline {
  text-align: left;
  min-width: 200px;
  max-width: 350px;
  white-space: pre-line;
}

/* Row styling */
.dynamic-table .table-row-even {
  background-color: #f8f9fa;
}

.dynamic-table .table-row-odd {
  background-color: white;
}

.dynamic-table tbody tr:hover {
  background-color: #e3f2fd !important;
  transition: background-color 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dynamic-table {
    font-size: 10px;
  }

  .dynamic-table .table-header {
    padding: 8px 4px;
    font-size: 9px;
  }

  .dynamic-table .table-cell {
    padding: 6px 4px;
    font-size: 9px;
  }

  /* Adjust cell widths for mobile */
  .cell-description,
  .cell-multiline {
    min-width: 150px;
    max-width: 200px;
  }

  .cell-text {
    min-width: 80px;
    max-width: 120px;
  }
}

.excel-data-container {
  max-width: 100%; /* Adjust to your preferred container width */
  overflow-x: auto;
}

.card.user {
  max-width: fit-content; /* Limits the card width */
  overflow: hidden; /* Prevents content from overflowing the card */
}
.form-control:focus {
  box-shadow: none;
}
/* Loading Indicator Container */
.loading-indicator {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.loader {
  display: flex;
  gap: 4px; /* Space between dots */
}

.loader span {
  width: 6px; /* Dot size */
  height: 6px;
  background-color: var(--primary); /* Use your theme's primary color */
  border-radius: 50%; /* Make it circular */
  animation: dot-flash 1.4s infinite ease-in-out;
}

.loader span:nth-child(1) {
  animation-delay: 0s;
}
.loader span:nth-child(2) {
  animation-delay: 0.2s;
}
.loader span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-flash {
  0%,
  80%,
  100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}

.shimmer {
  font-size: 16px;
  color: #888;
  position: relative;
  overflow: hidden;
  display: inline-block;
  background: linear-gradient(
    90deg,
    var(--text-primary) 0%,
    var(--black-500) 50%,
    var(--text-primary) 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: shimmer 1.5s linear infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.stop-btn {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  background-color: #ff4b4b; /* Red color to signify stopping */
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stop-btn:hover {
  background-color: #ff1f1f; /* Slightly darker red on hover */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* Enhance shadow on hover */
}

.stop-btn:active {
  background-color: #e60000; /* Even darker red on click */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Reset shadow */
}

.stop-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 75, 75, 0.5); /* Red focus ring for accessibility */
}
