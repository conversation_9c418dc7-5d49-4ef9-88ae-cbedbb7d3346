/**
 * Interface for AquaPro Integration API response
 */
export interface AquaProResponse {
  success: boolean;
  response: string; // JSON string that needs to be parsed
}

/**
 * Interface for the parsed integration results from the AquaPro response
 */
export interface AquaProIntegrationResult {
  status: string;
  operation: string;
  message: string;
  id: string;
}

/**
 * Interface for the parsed response content
 */
export interface AquaProResponseContent {
  integration_results: AquaProIntegrationResult[];
}
