# O-Bot - AI-Powered Chat Assistant

O<PERSON><PERSON><PERSON> is an Angular-based AI chat assistant application that provides intelligent conversational capabilities with integrated AquaPro system functionality. The application features a modern, responsive interface for seamless user interaction with AI-powered assistance.

## Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Development](#development)
- [Build & Deployment](#build--deployment)
- [Features](#features)
- [API Integration](#api-integration)
- [Icons & Assets](#icons--assets)
- [Architecture](#architecture)
- [Troubleshooting](#troubleshooting)

## Overview

O-Bot is a comprehensive chat application built with Angular 16 that provides:

- **AI-Powered Conversations**: Interactive chat interface with intelligent responses
- **AquaPro Integration**: Seamless integration with AquaPro systems for data processing
- **Multi-Feature Interface**: Includes notifications, prompt vault, pinned items, and recent chats
- **Responsive Design**: Modern UI with Bootstrap 4 styling
- **File Upload Support**: Document upload capabilities for enhanced interactions
- **Theme Support**: Light and dark mode options
- **Real-time Communication**: Live chat functionality with message persistence
- **.NET Framework Integration**: Designed for direct deployment on .NET Framework infrastructure

## Prerequisites

Before setting up O-Bot, ensure you have the following installed:

### System Requirements

- **Node.js**: Version 16.x or higher
- **npm**: Version 8.x or higher (comes with Node.js)
- **Angular CLI**: Version 16.2.16 or higher
- **Git**: For version control

### Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

### Development Tools (Optional)

- **Visual Studio Code**: Recommended IDE with Angular extensions
- **Yarn**: Alternative package manager (version 1.22.22 included)

## Installation

Follow these steps to set up O-Bot locally:

### 1. Clone the Repository

```bash
git clone <repository-url>
cd omnex_chat_fe
```

### 2. Install Dependencies

Using npm:

```bash
npm install
```

Or using yarn:

```bash
yarn install
```

### 3. Verify Installation

Check that Angular CLI is installed:

```bash
ng version
```

Expected output should show Angular CLI version 16.2.16 or higher.

## Configuration

### Environment Setup

O-Bot uses environment-specific configurations located in `src/environments/`:

#### Development Environment (`environment.development.ts`)

```typescript
export const environment = {
  production: false,
  apiUrl: "/api-proxy/aquapro_ai_agent/api/v1",
  assetBasePath: "/assets/",
};
```

#### Production Environment (`environment.ts`)

```typescript
export const environment = {
  production: true,
  apiUrl: "https://o-botagent.ewqims.com/aquapro_ai_agent/api/v1",
  assetBasePath: "/EwQIMS_Inst1/common/EwIMSNew/Scripts/client/assets/",
};
```

### Proxy Configuration

For development, the application uses a proxy configuration (`src/proxy.conf.json`) to handle API requests:

```json
{
  "/api-proxy": {
    "target": "https://o-botagent.ewqims.com",
    "secure": true,
    "changeOrigin": true,
    "logLevel": "debug",
    "pathRewrite": {
      "^/api-proxy": ""
    }
  }
}
```

### Asset Configuration

The application requires specific asset paths configured in `angular.json`:

- Development assets: `src/assets/`
- Production assets: `/EwQIMS_Inst1/common/EwIMSNew/Scripts/client/assets/`

## Development

### Starting the Development Server

Run the development server:

```bash
npm start
# or
ng serve
```

Navigate to `http://localhost:4200/`. The application will automatically reload when you make changes to the source files.

### Development Scripts

- `npm start` - Start development server
- `npm run build` - Build the project
- `npm run watch` - Build and watch for changes
- `npm run test` - Run unit tests
- `npm run prod` - Build for production

### Code Scaffolding

Generate new components:

```bash
ng generate component component-name
ng generate service service-name
ng generate module module-name
```

### Running Tests

Execute unit tests:

```bash
npm test
# or
ng test
```

Tests run via [Karma](https://karma-runner.github.io) and use Jasmine framework.

## Build & Deployment

### Production Build

Create a production build:

```bash
npm run prod
# or
ng build --configuration production
```

Build artifacts are stored in the `dist/o_bot/` directory.

### Build Configurations

#### Production Build Features:

- **Output Hashing**: All files for cache busting
- **Optimization**: Minification and tree-shaking
- **Bundle Budgets**:
  - Initial bundle: 1.35MB warning, 1.5MB error
  - Component styles: 5KB warning, 10KB error

#### Development Build Features:

- **Source Maps**: Enabled for debugging
- **Vendor Chunk**: Separate vendor bundle
- **Named Chunks**: For easier debugging
- **No Optimization**: Faster build times

### Deployment Steps

1. **Build the Application**:

   ```bash
   npm run prod
   ```

2. **Deploy Static Files**:
   Copy contents of `dist/o_bot/` to your web server

3. **Configure Web Server**:

   - Ensure proper MIME types for Angular files
   - Configure routing to serve `index.html` for all routes
   - Set up HTTPS (recommended)

4. **Environment Variables**:
   - Update API URLs in production environment
   - Configure asset paths for your deployment

### .NET Framework Deployment

This Angular application is designed to be deployed directly on .NET Framework infrastructure. The production build files integrate seamlessly with .NET applications.

#### .NET Framework Integration Steps

1. **Build for Production**:

   ```bash
   npm run prod
   ```

2. **Deploy to .NET Application**:

   - Copy all files from `dist/o_bot/` to your .NET web application directory
   - Typically deployed to: `wwwroot/` or `Content/` folder in your .NET project

3. **IIS Configuration**:

   - Ensure IIS has proper MIME types configured for Angular files
   - Configure URL rewriting for Single Page Application (SPA) routing

### Omnex System Deployment

For deployment on Omnex systems, follow these specific steps:

#### Omnex Deployment Process

1. **Build the Project**:

   ```bash
   npm run prod
   ```

   This creates optimized production files in the `dist/o_bot/` directory.

2. **Copy Distribution Files**:

   - Navigate to the `dist/o_bot/` folder after build completion
   - Copy all files and folders from `dist/o_bot/`
   - Paste these files into your Omnex **Scripts folder** as required by your system configuration

3. **Omnex Scripts Folder Structure**:

   ```
   Scripts/
   ├── index.html                 # Main application entry point
   ├── main.[hash].js             # Main application bundle
   ├── polyfills.[hash].js        # Browser polyfills
   ├── runtime.[hash].js          # Angular runtime
   ├── styles.[hash].css          # Application styles
   ├── assets/                    # Static assets
   │   ├── Icon/                  # Icon files
   │   ├── *.png                  # Image files
   │   └── ...
   └── 3rdpartylicenses.txt       # Third-party licenses
   ```

4. **Verify Asset Paths**:

   - Ensure the production environment configuration matches your Omnex deployment path
   - Update `environment.ts` if necessary:

   ```typescript
   export const environment = {
     production: true,
     apiUrl: "https://o-botagent.ewqims.com/aquapro_ai_agent/api/v1",
     assetBasePath: "/EwQIMS_Inst1/common/EwIMSNew/Scripts/client/assets/",
   };
   ```

5. **Omnex System Configuration**:
   - Configure your Omnex system to serve the Angular application from the Scripts folder
   - Ensure proper routing is set up to handle Angular's client-side routing
   - Verify that the Omnex server can serve static files (HTML, CSS, JS, SVG)

#### Omnex Deployment Checklist

- [ ] Production build completed successfully
- [ ] All files copied from `dist/o_bot/` to Omnex Scripts folder
- [ ] Asset paths configured correctly in environment files
- [ ] Omnex server configured to serve static files
- [ ] API endpoints accessible from Omnex environment
- [ ] Icons and images loading correctly
- [ ] Angular routing working properly
- [ ] AquaPro integration functional

#### Omnex-Specific Considerations

1. **File Permissions**: Ensure copied files have appropriate read permissions in the Omnex environment
2. **Path Configuration**: Verify that `assetBasePath` in environment configuration matches the actual deployment structure
3. **Server Configuration**: Configure the Omnex server to handle Angular's client-side routing
4. **API Connectivity**: Ensure the Omnex environment can reach the AquaPro API endpoints
5. **Asset Loading**: Verify that all icons, images, and static assets load correctly from the Scripts folder

6. **Web.config Configuration**:

   ```xml
   <?xml version="1.0" encoding="utf-8"?>
   <configuration>
     <system.webServer>
       <rewrite>
         <rules>
           <rule name="Angular Routes" stopProcessing="true">
             <match url=".*" />
             <conditions logicalGrouping="MatchAll">
               <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
               <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
             </conditions>
             <action type="Rewrite" url="/index.html" />
           </rule>
         </rules>
       </rewrite>
       <staticContent>
         <mimeMap fileExtension=".json" mimeType="application/json" />
         <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
         <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
       </staticContent>
     </system.webServer>
   </configuration>
   ```

7. **Asset Path Configuration**:
   - Update `environment.ts` to match your .NET application structure
   - Ensure `assetBasePath` points to correct .NET asset directory
   - Example: `/EwQIMS_Inst1/common/EwIMSNew/Scripts/client/assets/`

#### .NET MVC Integration

If integrating with .NET MVC application:

1. **Controller Setup**:

   ```csharp
   public class HomeController : Controller
   {
       public ActionResult Index()
       {
           return View();
       }

       // Catch-all route for Angular routing
       public ActionResult Angular()
       {
           return View("Index");
       }
   }
   ```

2. **Route Configuration**:
   ```csharp
   routes.MapRoute(
       name: "Angular",
       url: "o-bot/{*catchall}",
       defaults: new { controller = "Home", action = "Angular" }
   );
   ```

#### .NET Core Integration

For .NET Core applications:

1. **Startup.cs Configuration**:

   ```csharp
   public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
   {
       app.UseStaticFiles();
       app.UseRouting();

       app.UseEndpoints(endpoints =>
       {
           endpoints.MapFallbackToFile("index.html");
       });
   }
   ```

### Server Configuration Examples

#### IIS (web.config) - Recommended for .NET Framework

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="Angular Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/index.html" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
```

#### Apache (.htaccess)

```apache
RewriteEngine On
RewriteBase /
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

#### Nginx

```nginx
location / {
  try_files $uri $uri/ /index.html;
}
```

## Features

### Core Functionality

1. **Chat Interface**

   - Real-time messaging with AI assistant
   - Message history and conversation management
   - File upload support for documents
   - Conversation persistence across sessions

2. **AquaPro Integration**

   - Send chat responses to AquaPro system
   - Confirmation modal for data transmission
   - Integration status tracking and reporting
   - Error handling and user feedback

3. **Navigation & Organization**

   - **New Chat**: Start fresh conversations
   - **Notifications**: System alerts and updates
   - **Prompt Vault**: Saved prompts and templates
   - **Pinned Items**: Important conversations
   - **Recent Chats**: Chat history access
   - **Help & Support**: User assistance
   - **User Settings**: Personalization options

4. **User Interface**
   - Responsive sidebar navigation
   - Fullscreen mode toggle
   - Theme switching (light/dark)
   - User avatar and profile display
   - Loading states and progress indicators

### Advanced Features

- **Session Management**: Automatic session handling with cookies
- **Error Handling**: Comprehensive error reporting and user alerts
- **Logging**: Detailed application logging for debugging
- **Authentication**: HTTP interceptor for request authentication
- **Pagination**: Efficient data loading for large datasets

## API Integration

### AquaPro Integration API

The application integrates with AquaPro systems through a dedicated API:

#### Endpoint

```
POST /aquapro_ai_agent/api/v1/aquapro/
```

#### Parameters

- `message_id`: The bot message ID to process
- `attempt`: Attempt number (default: 1)

#### Usage Example

```typescript
this.chatService.createAquaProData(messageId, attempt).subscribe({
  next: (response) => {
    // Handle successful integration
    const results = parseAquaProResponse(response);
    console.log("Integration results:", results);
  },
  error: (error) => {
    // Handle integration errors
    console.error("AquaPro integration failed:", error);
  },
});
```

#### Response Format

```typescript
interface AquaProResponse {
  success: boolean;
  response: string; // JSON string containing integration results
}
```

### Chat API

#### Send Message

```
POST /aquapro_ai_agent/api/v1/chat/active2?attempt=1
```

#### Get Chat History

```
GET /aquapro_ai_agent/api/v1/chat/{chatId}?attempt=1
```

## Icons & Assets

O-Bot uses a comprehensive icon system with SVG icons for optimal scalability and performance. The application features both regular and filled/active states for interactive elements.

### Icon System Overview

The application uses a dynamic icon system that supports:

- **State-based Icons**: Regular and filled/active variants
- **Theme-aware Icons**: Icons that adapt to light/dark themes
- **Interactive States**: Hover and active state changes
- **Scalable SVG Format**: Vector graphics for crisp display at any size

### Asset Structure

```
src/assets/
├── Icon/                          # Main icon directory
│   ├── Bell.svg                   # Notifications (regular)
│   ├── Bell-Fill.svg              # Notifications (active)
│   ├── Plus.svg                   # New chat/add actions
│   ├── PushPin.svg                # Pin items (regular)
│   ├── PushPin-Fill.svg           # Pin items (active)
│   ├── Vault.svg                  # Prompt vault (regular)
│   ├── Vault-Fill.svg             # Prompt vault (active)
│   ├── Wrench.svg                 # Settings (regular)
│   ├── Wrench-Fill.svg            # Settings (active)
│   ├── ClockCounterClockwise.svg  # Recent chats
│   ├── SidebarSimple.svg          # Sidebar toggle
│   ├── ThumbsUp.svg               # Like feedback
│   ├── ThumbsUp-filled.svg        # Like feedback (active)
│   ├── ThumbsDown.svg             # Dislike feedback
│   ├── ThumbsDown-filled.svg      # Dislike feedback (active)
│   ├── Clipboard.svg              # Copy to clipboard
│   ├── Check-primary.svg          # Success states
│   ├── Check-white.svg            # Success states (white)
│   ├── Check-gray.svg             # Success states (gray)
│   ├── X.svg                      # Close/cancel actions
│   ├── X-primary.svg              # Close/cancel (primary color)
│   ├── Warning.svg                # Warning states
│   ├── Question.svg               # Help (regular)
│   ├── Question-Fill.svg          # Help (active)
│   ├── ArrowsOut.svg              # Fullscreen expand
│   ├── ArrowsIn.svg               # Fullscreen collapse
│   ├── ArrowsClockwise.svg        # Refresh/reload
│   ├── ArrowSquareOut.svg         # External links
│   ├── FileArrowUp.png            # File upload
│   ├── Scroll.svg                 # Scroll indicators
│   ├── NotePencil-gray.svg        # Edit actions
│   ├── chevron_left.svg           # Navigation arrows
│   │
│   ├── chat/                      # Chat-specific icons
│   │   ├── NotePencil.png         # Edit chat
│   │   ├── Trash.svg              # Delete chat
│   │   ├── arrow-up.svg           # Send message
│   │   ├── clip.svg               # Attach file
│   │   ├── comment.svg            # Comments
│   │   └── refresh.svg            # Refresh chat
│   │
│   ├── settings/                  # Theme/settings icons
│   │   ├── Monitor.svg            # System theme (regular)
│   │   ├── Monitor-primary.svg    # System theme (active)
│   │   ├── Sun.svg                # Light theme (regular)
│   │   ├── Sun-primary.svg        # Light theme (active)
│   │   ├── Moon.svg               # Dark theme (regular)
│   │   └── Moon-primary.svg       # Dark theme (active)
│   │
│   ├── prompt-vault/              # Prompt vault icons
│   │   ├── DotsThreeOutlineVertical.svg  # Menu options
│   │   ├── NavigationArrow.svg           # Navigation
│   │   ├── NotePencil.svg               # Edit prompt
│   │   └── ShareNetwork.svg             # Share prompt
│   │
│   └── pinned/                    # Pinned items icons
│       └── PushPinSlash.svg       # Unpin action
│
├── Logo-O-BOT-Small.png           # Small logo
├── O-BOT-logo.png                 # Main logo
└── avatar.png                     # Default user avatar
```

### Icon Implementation

#### Dynamic Icon Loading

Icons are loaded dynamically based on component state:

```typescript
// Example from o-bot.component.ts
links: Link[] = [
  {
    id: 1,
    pageName: 'Notifications',
    isActive: false,
    filledIcon: `${this.assetBasePath}Icon/Bell-Fill.svg`,
    regularIcon: `${this.assetBasePath}Icon/Bell.svg`,
  },
  {
    id: 2,
    pageName: 'Prompt Vault',
    isActive: false,
    filledIcon: `${this.assetBasePath}Icon/Vault-Fill.svg`,
    regularIcon: `${this.assetBasePath}Icon/Vault.svg`,
  }
];
```

#### Theme-Aware Icons

Settings icons adapt to theme selection:

```typescript
// Example from user-settings.component.ts
themeIcons = {
  System: {
    active: `${this.assetBasePath}Icon/settings/Monitor-primary.svg`,
    regular: `${this.assetBasePath}Icon/settings/Monitor.svg`,
  },
  Light: {
    active: `${this.assetBasePath}Icon/settings/Sun-primary.svg`,
    regular: `${this.assetBasePath}Icon/settings/Sun.svg`,
  },
  Dark: {
    active: `${this.assetBasePath}Icon/settings/Moon-primary.svg`,
    regular: `${this.assetBasePath}Icon/settings/Moon.svg`,
  },
};
```

#### Interactive Icon States

Icons change on hover and active states:

```html
<!-- Example from o-bot.component.html -->
<img
  [src]="
    hoveredLinkId === link.id || link.isActive
      ? link.filledIcon
      : link.regularIcon
  "
  class="pr-2"
  alt="..."
/>
```

### Asset Path Configuration

Asset paths are environment-specific:

#### Development Environment

```typescript
assetBasePath: "/assets/";
```

#### Production Environment (.NET Framework)

```typescript
assetBasePath: "/EwQIMS_Inst1/common/EwIMSNew/Scripts/client/assets/";
```

### Icon Categories

#### 1. Navigation Icons

- **Plus.svg**: New chat creation
- **SidebarSimple.svg**: Sidebar toggle
- **chevron_left.svg**: Back navigation
- **ArrowsOut.svg / ArrowsIn.svg**: Fullscreen toggle

#### 2. Feature Icons

- **Bell.svg / Bell-Fill.svg**: Notifications
- **Vault.svg / Vault-Fill.svg**: Prompt vault
- **PushPin.svg / PushPin-Fill.svg**: Pinned items
- **ClockCounterClockwise.svg**: Recent chats
- **Wrench.svg / Wrench-Fill.svg**: Settings

#### 3. Action Icons

- **ThumbsUp.svg / ThumbsUp-filled.svg**: Like feedback
- **ThumbsDown.svg / ThumbsDown-filled.svg**: Dislike feedback
- **Clipboard.svg**: Copy to clipboard
- **NotePencil.svg**: Edit actions
- **Trash.svg**: Delete actions

#### 4. Status Icons

- **Check-primary.svg / Check-white.svg / Check-gray.svg**: Success states
- **Warning.svg**: Warning/error states
- **X.svg / X-primary.svg**: Close/cancel actions

#### 5. Theme Icons

- **Monitor.svg / Monitor-primary.svg**: System theme
- **Sun.svg / Sun-primary.svg**: Light theme
- **Moon.svg / Moon-primary.svg**: Dark theme

### Adding New Icons

To add new icons to the application:

1. **Add SVG File**: Place the icon in the appropriate `src/assets/Icon/` subdirectory
2. **Update Component**: Reference the icon in your component
3. **Environment Path**: Use `assetBasePath` for proper path resolution

```typescript
// Example of adding a new icon
newIcon: string = `${this.assetBasePath}Icon/YourNewIcon.svg`;
```

### Icon Best Practices

1. **SVG Format**: Use SVG for scalability and performance
2. **Consistent Naming**: Follow the existing naming convention
3. **State Variants**: Provide both regular and active/filled variants
4. **Color Consistency**: Use consistent colors across icon sets
5. **Size Optimization**: Optimize SVG files for web delivery
6. **Accessibility**: Include appropriate alt text for screen readers

### Icon Color Scheme

- **Primary Color**: `#3ABAAB` (teal/aqua)
- **White**: `#FFFFFF` for dark backgrounds
- **Gray**: `#969696` for secondary states
- **Fill Colors**: Match the application's color palette

### Deployment Considerations

When deploying to .NET Framework:

1. **Asset Path**: Ensure `assetBasePath` points to correct .NET directory
2. **MIME Types**: Configure IIS to serve SVG files properly
3. **File Permissions**: Verify icon files have proper read permissions
4. **Build Process**: Icons are automatically copied during `ng build`

```xml
<!-- web.config MIME type for SVG -->
<staticContent>
  <mimeMap fileExtension=".svg" mimeType="image/svg+xml" />
</staticContent>
```

## Architecture

### Technology Stack

- **Frontend Framework**: Angular 16.2.0
- **UI Framework**: Bootstrap 4.3.1
- **HTTP Client**: Angular HttpClient with RxJS
- **Styling**: CSS3 with Bootstrap components
- **Build Tool**: Angular CLI with Webpack
- **Package Manager**: npm/yarn

### Project Structure

```
src/
├── app/
│   ├── component/          # Reusable UI components
│   ├── pages/             # Main application pages
│   │   └── o-bot/         # O-Bot main interface
│   │       ├── chat/      # Chat functionality
│   │       ├── notifications/
│   │       ├── prompt-vault/
│   │       └── ...
│   ├── services/          # Business logic services
│   ├── interfaces/        # TypeScript interfaces
│   ├── interceptors/      # HTTP interceptors
│   ├── utils/            # Utility functions
│   └── examples/         # Usage examples
├── assets/               # Static assets
├── environments/         # Environment configurations
└── styles.css           # Global styles
```

### Key Services

- **ChatService**: Manages chat functionality and API communication
- **AlertService**: Handles user notifications and alerts
- **ThemeSwitchService**: Manages application theming
- **LogService**: Centralized logging functionality
- **UserInfoService**: User profile and session management

### Data Flow

1. User interacts with chat interface
2. ChatService processes user input
3. HTTP requests sent to backend API
4. Responses processed and displayed
5. Optional AquaPro integration triggered
6. Results displayed to user with appropriate feedback

## Troubleshooting

### Common Issues

#### 1. Development Server Won't Start

**Problem**: `ng serve` fails to start

**Solutions**:

```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version  # Should be 16.x or higher
```

#### 2. API Connection Issues

**Problem**: API requests failing in development

**Solutions**:

- Verify proxy configuration in `src/proxy.conf.json`
- Check network connectivity to target API
- Ensure CORS is properly configured on the backend
- Verify API endpoints in environment files

#### 3. Build Errors

**Problem**: Production build fails

**Solutions**:

```bash
# Check for TypeScript errors
ng build --configuration development

# Verify all dependencies
npm audit fix

# Check bundle size limits in angular.json
```

#### 4. Asset Loading Issues

**Problem**: Images or assets not loading

**Solutions**:

- Verify asset paths in environment configurations
- Check `angular.json` asset configuration
- Ensure assets exist in specified directories
- Verify web server configuration for static files

#### 5. AquaPro Integration Failures

**Problem**: AquaPro integration not working

**Solutions**:

- Check message ID availability
- Verify API endpoint configuration
- Review network connectivity
- Check authentication credentials
- Examine browser console for detailed errors

#### 6. .NET Framework Deployment Issues

**Problem**: Angular app not loading after .NET deployment

**Solutions**:

```xml
<!-- Ensure web.config has proper URL rewriting -->
<system.webServer>
  <rewrite>
    <rules>
      <rule name="Angular Routes" stopProcessing="true">
        <match url=".*" />
        <conditions logicalGrouping="MatchAll">
          <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
          <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
        </conditions>
        <action type="Rewrite" url="/index.html" />
      </rule>
    </rules>
  </rewrite>
</system.webServer>
```

**Problem**: Static files (CSS/JS) not loading

**Solutions**:

- Verify file paths in `angular.json` match .NET directory structure
- Check IIS MIME types configuration
- Ensure proper permissions on deployed files
- Verify `base href` in `index.html` matches deployment path

**Problem**: API calls failing in .NET environment

**Solutions**:

- Update `environment.ts` with correct production API URLs
- Configure CORS in .NET backend if needed
- Check proxy settings are not interfering
- Verify authentication tokens are properly handled

#### 7. IIS-Specific Issues

**Problem**: 404 errors on Angular routes

**Solutions**:

- Install URL Rewrite module for IIS
- Verify web.config is in the correct directory
- Check application pool settings
- Ensure .NET Framework version compatibility

**Problem**: MIME type errors

**Solutions**:

```xml
<staticContent>
  <mimeMap fileExtension=".json" mimeType="application/json" />
  <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
  <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
  <mimeMap fileExtension=".js" mimeType="application/javascript" />
</staticContent>
```

#### 8. Omnex System Deployment Issues

**Problem**: Application not loading after copying to Omnex Scripts folder

**Solutions**:

- Verify all files were copied from `dist/o_bot/` including subdirectories
- Check file permissions in the Omnex Scripts folder
- Ensure Omnex server is configured to serve static files
- Verify the Scripts folder path is accessible via web browser

**Problem**: Icons and assets not displaying in Omnex environment

**Solutions**:

- Check `assetBasePath` configuration in `environment.ts`
- Verify asset files are present in the Scripts folder
- Ensure Omnex server can serve SVG and image files
- Test direct access to asset URLs in browser

**Problem**: Angular routing not working in Omnex system

**Solutions**:

- Configure Omnex server to handle client-side routing
- Ensure `index.html` is served for all non-file routes
- Check if URL rewriting is properly configured
- Verify base href in `index.html` matches deployment path

**Problem**: API calls failing in Omnex environment

**Solutions**:

- Verify API URLs in production environment configuration
- Check network connectivity from Omnex system to API endpoints
- Ensure CORS is configured properly on the backend
- Test API endpoints directly from Omnex environment

**Omnex Deployment Verification Steps**:

1. Access the application via Omnex system URL
2. Verify all icons and images load correctly
3. Test navigation between different sections
4. Confirm chat functionality works
5. Verify AquaPro integration is functional
6. Check browser console for any errors

### Debug Mode

Enable debug logging by setting localStorage:

```javascript
localStorage.setItem("debug", "true");
```

### Performance Optimization

- Use `ng build --prod` for optimized builds
- Implement lazy loading for large modules
- Optimize images and assets
- Monitor bundle sizes with webpack-bundle-analyzer

### Getting Help

- Check browser console for error messages
- Review Angular CLI documentation
- Examine network tab for API request issues
- Use Angular DevTools for component debugging

For additional support, refer to the [Angular CLI Overview and Command Reference](https://angular.io/cli).
