<div class="d-flex flex-colum align-items-center py-2 px-2" *ngIf="isLoading">
  <div
    class="col justify-content-center text-center align-items-center flex-fill"
  >
    <div class="text-center">
      <p>Loading...</p>
    </div>
  </div>
</div>

<div
  class="d-flex flex-colum align-items-center py-2 px-2"
  *ngIf="totalCount == 0 && !isLoading"
>
  <div
    class="col justify-content-center text-center align-items-center flex-fill"
  >
    <div class="text-center">
      <h6>No Pinned Items</h6>
    </div>
  </div>
</div>

<div *ngFor="let date of getKeys(groupedEntries)" class="mb-3">
  <h6>{{ date }}</h6>
  <hr />
  <div *ngFor="let entry of groupedEntries[date]; let i = index">
    <div class="col px-0 d-flex pb-2">
      <div class="card flex-fill border-0 shadow1">
        <div class="card-body p-2">
          <div class="d-flex flex-row justify-content-between">
            <div
              class="col px-2 mb-0 body2 ellipsis text-nowrap cursor-pointer"
              (click)="startChat(entry.chat_id); toChatPage('New Chat')"
            >
              <h6 class="mb-0 cursor-pointer">{{ entry.title }}</h6>
              <!-- <span class="small">{{ date }}</span> -->
            </div>

            <div
              class="col-auto px-1 d-flex flex-row table-hover cursor-pointer"
            >
              <div (click)="unPinEntry(i)">
                <img src="{{ assetBasePath }}Icon/pinned/PushPinSlash.svg" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Pagination Controls -->

<!-- Pagination Component -->
<app-pagination
  [totalCount]="totalCount"
  [itemsPerPage]="itemsPerPage"
  [currentPage]="currentPage"
  (pageChange)="onPageChange($event)"
></app-pagination>
