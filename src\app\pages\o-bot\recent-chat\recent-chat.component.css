hr {
  margin-top: 0.5rem;
}
/* Pagination container */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Pagination buttons */
.pagination-btn {
  background-color: #f1f1f1;
  border: 1px solid #ccc;
  padding: 8px 12px;
  margin: 0 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.pagination-btn:hover {
  background-color: #ddd;
}

.pagination-btn:disabled {
  background-color: #e0e0e0;
  cursor: not-allowed;
}

/* Active page number */
.pagination-number {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  margin: 0 4px;
}

.pagination-number.active {
  background-color: #007bff;
  color: white;
  font-weight: bold;
}

.pagination-number:hover {
  background-color: #f0f0f0;
}

/* Additional pagination info */
.pagination-info {
  font-size: 14px;
  margin-top: 10px;
}

.pagination-info .form-control-sm {
  width: 60px;
  margin-left: 10px;
}

.pagination-info div {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Hide "Jump to" box if there are fewer than two pages */
@media (max-width: 600px) {
  .pagination-info {
    display: none;
  }
}
