import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { AlertService } from 'src/app/services/alert/alert.service';
import { ChatService } from 'src/app/services/chat/chat.service';
import { DateService } from 'src/app/services/date/date.service';
import { LogService } from 'src/app/services/log/log.service';
import { PageSelectionService } from 'src/app/services/page-select/page-selection.service';
import { environment } from 'src/environments/environment.development';

@Component({
  selector: 'app-recent-chat',
  templateUrl: './recent-chat.component.html',
  styleUrls: ['./recent-chat.component.css'],
})
export class RecentChatComponent {
  constructor(
    private alertService: AlertService,
    private chatService: ChatService,
    private pageSelectionService: PageSelectionService,
    private http: HttpClient,
    private dateService: DateService,
    private logService: LogService
  ) {}

  entries: any = [];
  chat_id: string = '';
  url: string = environment.apiUrl;

  private baseUrl = `${this.url}/chat`;

  totalCount: number = 0;
  currentPage: number = 1;
  itemsPerPage = 10;

  editIndex: number | null = null; // Track the index of the entry being edited
  editedName: string = ''; // Store the edited name temporarily

  groupedEntries: { [date: string]: any[] } = {};

  isLoading: boolean = true;
  assetBasePath: string = environment.assetBasePath;

  ngOnInit() {
    this.getChatListApi();
  }

  deleteEntry(index: number) {
    const chatId = this.entries[index].chat_id;
    const deleteUrl = `${this.baseUrl}/${chatId}`;

    this.isLoading = true;
    this.http.delete(deleteUrl, { withCredentials: true }).subscribe(
      () => {
        this.entries.splice(index, 1); // Remove the entry locally
        this.alertService.showAlert(
          'Chat entry deleted successfully!',
          'success'
        );
        this.groupEntriesByDate();
        this.isLoading = false;
      },
      (error) => {
        this.logService.error('Error deleting entry:' + error);
        this.alertService.showAlert('Failed to delete chat entry.', 'error');
        this.isLoading = false;
      }
    );
  }

  startEdit(index: number) {
    this.editIndex = index; // Set the index of the entry being edited
    this.editedName = this.entries[index].title; // Pre-fill the input with the current chat name
  }

  saveEdit() {
    if (this.editIndex !== null && this.editedName.trim()) {
      const chatId = this.entries[this.editIndex].chat_id;
      const updateUrl = `${this.baseUrl}/${chatId}`;
      const updatedData = { title: this.editedName };
      this.isLoading = true;
      this.http
        .put(updateUrl, updatedData, { withCredentials: true })
        .subscribe(
          () => {
            this.entries[this.editIndex!].title = this.editedName; // Update the entry locally
            this.editIndex = null; // Clear the edit state
            this.alertService.showAlert(
              'Chat name updated successfully!',
              'success'
            );
            this.groupEntriesByDate();
            this.isLoading = false;
          },
          (error) => {
            this.logService.error('Error updating entry:' + error);
            this.alertService.showAlert('Failed to update chat name.', 'error');
            this.isLoading = false;
          }
        );
    }
  }

  startChat(query: string) {
    this.chatService.getRecentChat(query);
    this.chatService.getPreviusChat(query).then(() => {
      this.pageSelectionService.setSelectedPage('New Chat');
    });
  }

  toChatPage(page: string) {
    this.pageSelectionService.setSelectedPage(page);
    if (page === 'New Chat') {
      this.chatService.clearChatState(); // Clear state when moving to New Chat
    }
  }

  getChatListApi(): void {
    const skip = (this.currentPage - 1) * this.itemsPerPage;
    const limit = this.itemsPerPage;
    const apiUrl = `${this.baseUrl}/recent?skip=${skip}&limit=${limit}`;
    this.logService.log('Fetching prompts from:' + apiUrl); // Debugging API call
    this.isLoading = true;
    this.http.get<any>(apiUrl, { withCredentials: true }).subscribe(
      (response) => {
        if (response && response.recent_chats) {
          this.logService.log(response);
          this.entries = response.recent_chats;
          this.totalCount = response.total_count;
          this.groupEntriesByDate();
        }
        this.isLoading = false;
      },
      (error) => {
        this.logService.error('Error fetching prompts:' + error);
        this.alertService.showAlert(
          'Failed to load prompts. Please try again later.',
          'error'
        );
      }
    );
  }
  // Group entries by their respective dates
  groupEntriesByDate(): void {
    this.groupedEntries = {}; // Clear previous groupings
    this.entries.forEach((entry: any) => {
      const date = this.dateService.formatDate(entry.created_at); // Format entry date
      if (!this.groupedEntries[date]) {
        this.groupedEntries[date] = [];
      }
      this.groupedEntries[date].push(entry);
    });
  }
  getKeys(obj: any): string[] {
    return Object.keys(obj);
  }

  onPageChange(newPage: number) {
    this.currentPage = newPage;
    this.getChatListApi();
  }
}
