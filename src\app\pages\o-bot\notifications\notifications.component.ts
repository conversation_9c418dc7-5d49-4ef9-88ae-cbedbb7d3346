import { Component } from '@angular/core';
import { environment } from 'src/environments/environment.development';
interface Notification {
  id: number;
  avatar?: string; // Optional avatar image URL
  sender: string;
  message: string;
  date: string;
  isRead: boolean;
}

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.css'],
})
export class NotificationsComponent {
  assetBasePath: string = environment.assetBasePath;
  notifications: Notification[] = [
    {
      id: 1,
      sender: '<PERSON>',
      message: '<PERSON> has shared a new prompt with you.',
      date: '2024-10-29',
      isRead: false,
    },
    {
      id: 2,
      avatar: `${this.assetBasePath}avatar.png`,
      sender: '<PERSON>',
      message: '<PERSON> has shared a new prompt with you.',
      date: '2024-10-28',
      isRead: true,
    },
    // Add other entries as needed
  ];

  markAsRead(index: number) {
    this.notifications[index].isRead = true;
  }

  markAsUnread(index: number) {
    this.notifications[index].isRead = false;
  }

  unPinEntry(index: number) {
    this.notifications.splice(index, 1); // Remove the entry at the specified index
  }

  // Helper function to get initials from the sender's name
  getInitials(sender: string): string {
    return sender
      .split(' ')
      .map((name) => name.charAt(0))
      .join('')
      .toUpperCase();
  }
}
