import { Component } from '@angular/core';
import { HelpSupportService } from 'src/app/services/help/help-support.service';
import { environment } from 'src/environments/environment.development';

@Component({
  selector: 'app-help-support',
  templateUrl: './help-support.component.html',
  styleUrls: ['./help-support.component.css'],
})
export class HelpSupportComponent {
  faqs: string[] = [
    'How can the Omnex Agent help me create new documents?',
    'Can the Omnex Agent handle multiple types of documents?',
    'Do I need to constantly monitor the Omnex Agent?',
    'Can the Omnex Agent work with existing documents?',
  ];
  answers: { [key: string]: string } = {
    'How can the Omnex Agent help me create new documents?':
      'The Omnex Agent can guide you through the document creation process by providing templates and best practices.',
    'Can the Omnex Agent handle multiple types of documents?':
      'Yes, the Omnex Agent is designed to support various document types, including reports, proposals, and presentations.',
    'Do I need to constantly monitor the Omnex Agent?':
      'No, the Omnex Agent can operate autonomously and notify you of important updates.',
    'Can the Omnex Agent work with existing documents?':
      'Yes, the Omnex Agent can integrate with your existing documents and help optimize them.',
  };

  selectedFaq: string | null = null; // Store the selected FAQ
  showActiveSupport = false; // Toggle to show Active Support
  assetBasePath: string = environment.assetBasePath;

  constructor(private helpSupportService: HelpSupportService) {}

  onSelectFaq(faq: string) {
    this.selectedFaq = faq; // Set selected FAQ
    this.showActiveSupport = true; // Show active support view
  }
  onViewMyTickets() {
    this.selectedFaq = null; // Clear the selected FAQ
    this.showActiveSupport = true; // Show active support view
  }

  onFeedbackGiven(isHelpful: boolean) {
    if (isHelpful) {
    } else {
    }
    this.selectedFaq = null; // Reset selection
    this.showActiveSupport = false; // Go back to FAQs
  }
}
