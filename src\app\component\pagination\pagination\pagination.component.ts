import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-pagination',
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.css'],
})
export class PaginationComponent implements OnInit {
  @Input() totalCount: number = 0; // Total number of items
  @Input() itemsPerPage: number = 10; // Items per page
  @Input() currentPage: number = 1; // Current active page

  @Output() pageChange = new EventEmitter<number>(); // Emit the page number when it changes

  totalPages: number = 0;
  visiblePages: number[] = [];
  jumpToPage: number | null = null;
  Math: any = Math; // Allow access to Math in the template for min/max page calculation

  ngOnInit(): void {
    this.calculateTotalPages();
    this.updateVisiblePages();
  }

  ngOnChanges(): void {
    this.calculateTotalPages();
    this.updateVisiblePages();
  }

  calculateTotalPages() {
    this.totalPages = Math.ceil(this.totalCount / this.itemsPerPage);
  }

  updateVisiblePages() {
    const startPage = Math.max(1, this.currentPage - 2);
    const endPage = Math.min(this.totalPages, this.currentPage + 2);

    this.visiblePages = [];
    for (let i = startPage; i <= endPage; i++) {
      this.visiblePages.push(i);
    }
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.pageChange.emit(this.currentPage);
      this.updateVisiblePages();
    }
  }

  goToFirstPage() {
    this.goToPage(1);
  }

  goToLastPage() {
    this.goToPage(this.totalPages);
  }

  goToNextPage() {
    if (this.currentPage < this.totalPages) {
      this.goToPage(this.currentPage + 1);
    }
  }

  goToPreviousPage() {
    if (this.currentPage > 1) {
      this.goToPage(this.currentPage - 1);
    }
  }

  goToSpecificPage() {
    if (
      this.jumpToPage !== null &&
      this.jumpToPage >= 1 &&
      this.jumpToPage <= this.totalPages
    ) {
      this.goToPage(this.jumpToPage);
    }
    this.jumpToPage = null; // Reset after use
  }
}
