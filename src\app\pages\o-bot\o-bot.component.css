.o-bot-window-side {
  position: absolute;
  right: 0;
  width: 28%;
  /* width: 100%; */
  height: -moz-calc(100%);
  height: -webkit-calc(100%);
  height: 100%;
}
@media (max-width: 767px) {
  .o-bot-window-side {
    width: 100%;
  }
}

.o-bot-window-side.full-screen {
  width: calc(100% - 210px);
}
.bg-o-bot {
  height: -moz-calc(100% - (34px));
  height: -webkit-calc(100% - (34px));
  height: calc(100% - (34px));
  overflow-y: scroll;
  width: auto;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}
.ellipsis.full-screen {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.max-width-50 {
  max-width: 50%;
}
.max-width-90 {
  max-width: 90%;
}
.sidebar-small {
  position: absolute;
  right: 0;
  width: 50%;
  z-index: 1;
  height: -moz-calc(100% - (56px));
  height: -webkit-calc(100% - (56px));
  height: calc(100% - (56px));
  border-left: 1px solid var(--divider);

  transform: translateX(100%);
  -webkit-transform: translateX(100%);
  transition: transform 0.5s;
}
.sidebar-web {
  position: fixed; /* Changed from relative to fixed */
  left: 0; /* Aligns it to the top of the viewport */
  width: 210px;
  height: -moz-calc(100%);
  height: -webkit-calc(100%);
  height: calc(100%); /* Sets the height to full viewport */
  border-right: 1px solid var(--divider);
  z-index: 1;
}
.slide-in {
  animation: slide-in 0.5s forwards;
  -webkit-animation: slide-in 0.5s forwards;
}

.slide-out {
  display: none;
  animation: slide-out 0.5s forwards;
  -webkit-animation: slide-out 0.5s forwards;
}

@keyframes slide-in {
  100% {
    transform: translateX(0%);
  }
}

@-webkit-keyframes slide-in {
  100% {
    -webkit-transform: translateX(0%);
  }
}

@keyframes slide-out {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

@-webkit-keyframes slide-out {
  0% {
    -webkit-transform: translateX(0%);
  }
  100% {
    -webkit-transform: translateX(100%);
  }
}
.avatar {
  width: 38px;
  height: 38px;
  font-size: 18px;
  color: white;
  background-color: #007bff; /* Default color */
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  text-transform: uppercase;
  font-weight: bold;
}
