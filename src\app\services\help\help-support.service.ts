import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class HelpSupportService {
  constructor() {}

  private selectedFaqSubject = new BehaviorSubject<string | null>(null);
  selectedFaq$ = this.selectedFaqSubject.asObservable();

  selectFaq(faq: string) {
    this.selectedFaqSubject.next(faq);
  }

  clearSelection() {
    this.selectedFaqSubject.next(null);
  }
  getAnswerForFaq(faq: string): string {
    const answers: { [key: string]: string } = {
      'How can the Omnex Agent help me create new documents?':
        'The Omnex Agent can guide you through the document creation process by providing templates and best practices.',
      'Can the Omnex Agent handle multiple types of documents?':
        'Yes, the Omnex Agent is designed to support various document types, including reports, proposals, and presentations.',
      'Do I need to constantly monitor the Omnex Agent?':
        'No, the Omnex Agent can operate autonomously and notify you of important updates.',
      'Can the Omnex Agent work with existing documents?':
        'Yes, the Omnex Agent can integrate with your existing documents and help optimize them.',
    };

    return answers[faq] || 'No answer available.';
  }
}
