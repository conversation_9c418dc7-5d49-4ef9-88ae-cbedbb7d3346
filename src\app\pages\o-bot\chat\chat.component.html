<!-- Chat Landing page -->
<div
  bg-o-bot
  [ngClass]="
    this.fullScreenToggle == true
      ? 'chat-container '
      : 'chat-container fullscreen-view'
  "
  #chatContainer
>
  <div *ngIf="!isChatActive && !isLoading" class="col">
    <div class="col px-0 justify-content-center text-center">
      <div class="text-center">
        <img
          src="{{ assetBasePath }}O-BOT-logo.png"
          height="80px"
          width="80px"
        />
        <h1 class="dark-blue capitalize" style="text-transform: capitalize">
          Hello {{ userName }}!
        </h1>
        <h1>How can I help you today?</h1>
      </div>
    </div>
    <div>
      <div class="row justify-content-center px-0 pt-2">
        <!-- <div
        class="col-6 px-2 d-flex"
        *ngFor="let suggestion of suggestions"
        style="max-width: 175px"
      >
        <div
          class="card flex-fill border-0 shadow1 mb-3"
          (click)="startChat(suggestion)"
        >
          <div class="card-body p-2">
            <img src="{{ assetBasePath }}Icon/chat/comment.svg" />
            <div class="px-0 mb-0 body2">{{ suggestion }}</div>
          </div>
        </div>
      </div> -->
        <div
          class="col-6 px-2 d-flex"
          *ngFor="let suggestion of suggestions"
          style="max-width: 175px"
        >
          <div
            class="card flex-fill border-0 shadow1 mb-3"
            (click)="startChat(suggestion)"
          >
            <div class="card-body p-2">
              <img src="{{ assetBasePath }}Icon/chat/comment.svg" />
              <h6 class="px-0 mb-0 body1">{{ suggestion }}</h6>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="d-flex justify-content-center">
    <div class="btn btn-sm cursor-pointer" (click)="suggestMore()">
      <h6 class="mb-0">
        Suggest More
        <img
          src="{{ assetBasePath }}Icon/chat/refresh.svg"
          height="14px"
          width="14px"
        />
      </h6>
    </div>
  </div> -->
    <div class="container-fluid justify-content-center">
      <div
        class="col px-0 mr-2"
        [ngClass]="
          this.fullScreenToggle == true
            ? 'input-query fullscreen-view'
            : 'input-query'
        "
      >
        <div class="col px-1 bg-white rounded-lg">
          <div class="flex-fill px-2">
            <textarea
              class="form-control border-0"
              type="text"
              rows="2"
              placeholder="Enter your query here"
              [(ngModel)]="userInput"
            ></textarea>
          </div>
          <div
            class="d-flex flex-row justify-content-between align-items-center"
          >
            <div class="d-flex flex-row">
              <div
                class="p-2 rounded col cursor-pointer"
                (click)="fileInput.click()"
              >
                <img
                  src="{{ assetBasePath }}Icon/chat/clip.svg"
                  height="24px"
                  width="24px"
                />
                <input
                  #fileInput
                  type="file"
                  accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                  (change)="onFileChange($event)"
                  style="opacity: 0; position: absolute; z-index: -1"
                />
              </div>

              <!-- Display Attached File -->

              <div
                *ngIf="attachedFileName"
                class="d-flex align-items-center border-primary rounded-lg px-2 position-relative"
              >
                <div
                  class="bg-primary rounded-lg align-items-center d-flex justify-content-center"
                  style="min-height: 24px; min-width: 24px; max-height: 24px"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    fill="#000000"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M224,48H32a8,8,0,0,0-8,8V192a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A8,8,0,0,0,224,48ZM40,112H80v32H40Zm56,0H216v32H96ZM216,64V96H40V64ZM40,160H80v32H40Zm176,32H96V160H216v32Z"
                    ></path>
                  </svg>
                </div>
                <span class="ml-2 text-muted">{{ attachedFileName }}</span>
                <div
                  class="rounded-circle bg-primary position-absolute text-center align-items-center justify-content-center text-white d-flex cursor-pointer"
                  (click)="removeAttachment()"
                  style="
                    min-width: 20px;
                    max-width: 20px;
                    min-height: 20px;
                    max-height: 20px;
                    top: -10px;
                    right: -10px;
                  "
                >
                  x
                </div>
              </div>
            </div>

            <div
              class="p-2 rounded-lg bg-primary m-1"
              (click)="sendMessage()"
              *ngIf="!isTyping && userInput != ''"
            >
              <img src="{{ assetBasePath }}Icon/chat/arrow-up.svg" />
            </div>
            <div
              class="p-2 rounded-lg bg-primary-500 m-1"
              *ngIf="isTyping || userInput == ''"
            >
              <img src="{{ assetBasePath }}Icon/chat/arrow-up.svg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Active chat -->
  <div class="d-flex flex-colum align-items-center py-2 px-2" *ngIf="isLoading">
    <div
      class="col justify-content-center text-center align-items-center flex-fill"
    >
      <div class="text-center">
        <p>Loading...</p>
      </div>
    </div>
  </div>
  <div *ngIf="isChatActive" class="col px-0">
    <div *ngFor="let message of messages; let i = index" class="">
      <div class="d-flex flex-column">
        <div
          *ngIf="message.sender === 'user'"
          class="d-flex flex-row justify-content-end"
          (mouseenter)="onMouseEnter(i)"
          (mouseleave)="onMouseLeave(i)"
        >
          <div class="cursor-ponter rounded-lg">
            <button
              class="btn btn-sm px-1"
              (click)="
                addPrompt(i, 4, 'personal', message);
                onUserMessageClick(message)
              "
            >
              <img
                src="{{ assetBasePath }}Icon/Vault-gray.svg"
                width="20px"
                height="20px"
              />
            </button>
          </div>
          <!-- <div *ngIf="hoverStates[i]" class="cursor-ponter rounded-lg">
          <button class="btn btn-sm px-1">
            <img
              src="{{ assetBasePath }}Icon/NotePencil-gray.svg"
              width="20px"
              height="20px"
            />
          </button>
        </div> -->
          <div
            class="card user flex-fill border-0 shadow1 bg-primary-600"
            style="border-bottom-right-radius: 0"
          >
            <div class="card-body p-2 max-width-50">
              <div class="px-0 mb-0 body2">{{ message.text }}</div>
            </div>
          </div>
        </div>
        <div *ngIf="i === messages.length - 1 && !isrefreshing">
          <div class="d-flex flex-row py-4" *ngIf="isTyping">
            <div class="mr-2">
              <img
                src="{{ assetBasePath }}Logo-O-BOT-Small.png"
                width="24px"
                height="24px"
              />
            </div>
            <div class="loading-indicator">
              <span class="shimmer">Analyzing</span>
              <div class="loader">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
        <div
          *ngIf="message.sender === 'bot'"
          class="py-4"
          (mouseenter)="onMouseEnter(i)"
          (mouseleave)="onMouseLeave(i)"
        >
          <div class="d-flex flex-row">
            <div class="mr-2">
              <img
                src="{{ assetBasePath }}Logo-O-BOT-Small.png"
                width="24px"
                height="24px"
              />
            </div>

            <div
              class="card border-0 shadow1"
              style="border-top-left-radius: 0"
            >
              <div class="card-body p-2">
                <div
                  class="px-0 mb-0 body2"
                  [innerHTML]="formatToHTML(message.text)"
                ></div>

                <!-- Display buttons if present and visible -->
                <div *ngIf="message.buttons && message.buttonsVisible">
                  <div *ngFor="let button of message.buttons" class="">
                    <button
                      *ngIf="button.action == 'openModalDoc'"
                      data-toggle="modal"
                      data-target="#openModalDoc"
                      class="bg-primary-700 hover border-primary rounded-lg mb-2"
                    >
                      <div class="d-flex flex-row align-items-center">
                        <div
                          class="p-4 bg-primary-500"
                          style="border-radius: 0.3rem 0 0 0.3rem"
                        >
                          <img
                            src="{{ assetBasePath }}Icon/Scroll.svg"
                            width="24px"
                            height="24px"
                          />
                        </div>
                        <div
                          class="d-flex flex-column px-2 bg-primary-700 text-left"
                          style="border-radius: 0 0.3rem 0.3rem 0"
                        >
                          <h6 class="mb-1">
                            {{ button.label }}
                          </h6>
                          <p class="text-primary mb-0">
                            Click to open document
                          </p>
                        </div>
                      </div>
                    </button>
                    <div class="mb-2 d-flex flex-row">
                      <div>
                        <button
                          *ngIf="button.action == 'openModalConfirm'"
                          class="btn btn-sm btn-dark-blue mr-n2"
                          (click)="handleButtonAction(button.action)"
                          data-toggle="modal"
                          data-target="#openModalConfirm"
                        >
                          {{ button.label }}
                        </button>
                      </div>
                      <div class="d-flex flex-row">
                        <div>
                          <button class="btn btn-sm btn-outline-primary mr-2">
                            <img
                              src="{{ assetBasePath }}Icon/X-primary.svg"
                              class="mr-2"
                            />
                            NO
                          </button>
                        </div>
                        <div>
                          <button class="btn btn-sm btn-primary mr-2">
                            <img
                              src="{{ assetBasePath }}Icon/Check-white.svg"
                              class="mr-2"
                            />
                            YES
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div class="d-flex flex-row mt-1">
                  <div>
                    <button class="btn btn-sm btn-outline-primary mr-2">
                      <img
                        src="{{ assetBasePath }}Icon/X-primary.svg"
                        class="mr-2"
                      />
                      NO
                    </button>
                  </div>
                  <div>
                    <button
                      class="btn btn-sm btn-primary mr-2"
                      (click)="pinChat()"
                    >
                      <img
                        src="{{ assetBasePath }}Icon/Check-white.svg"
                        class="mr-2"
                      />
                      YES
                    </button>
                  </div>
                </div> -->

                <!-- Display table if it exists and is visible -->
                <div *ngIf="message.extraData != null">
                  <button
                    class="btn btn-sm btn-dark-blue mr-n2 text-white my-1"
                    data-toggle="modal"
                    data-target="#openModalConfirm"
                    (click)="prepareAquaProSend(message)"
                  >
                    Send To AquaPro
                  </button>
                  <button
                    class="btn btn-sm btn-secondary mr-n2 ml-4 text-primarymy-1"
                    (click)="
                      downloadFile(
                        message.extraData.content,
                        message.extraData.filename
                      )
                    "
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 512 512"
                      width="18px"
                      height="18px"
                    >
                      <!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2024 Fonticons, Inc.-->
                      <path
                        d="M288 32c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 242.7-73.4-73.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l128 128c12.5 12.5 32.8 12.5 45.3 0l128-128c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L288 274.7 288 32zM64 352c-35.3 0-64 28.7-64 64l0 32c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-32c0-35.3-28.7-64-64-64l-101.5 0-45.3 45.3c-25 25-65.5 25-90.5 0L165.5 352 64 352zm368 56a24 24 0 1 1 0 48 24 24 0 1 1 0-48z"
                      />
                    </svg>
                    Download
                  </button>
                </div>
                <!-- <div *ngIf="message.excelData && message.excelData.length > 0">
                  <p>No data available to display.</p>
                </div> -->
                <div
                  [ngClass]="
                    fullScreenToggle ? 'table-Width' : 'table-Width sideWindow'
                  "
                  *ngIf="message.excelData && message.excelData.length > 0"
                  class="table-container"
                >
                  <table class="table table-striped mt-2">
                    <thead class="bg-primary-200">
                      <tr>
                        <th *ngFor="let header of message.excelData[0]">
                          {{ header }}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let row of message.excelData.slice(1)">
                        <td *ngFor="let cell of row">{{ cell }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <!--Response action buttons-->
          <div
            class="d-flex flex-row justify-content-end position-absolute ml-4"
            *ngIf="i === messages.length - 1 || hoverStates[i]"
          >
            <div class="cursor-ponter rounded-lg">
              <button class="btn btn-sm px-1" (click)="onClipboardClick(i)">
                <img
                  [src]="
                    clipboardState[i]
                      ? assetBasePath + 'Icon/Check-gray.svg'
                      : assetBasePath + 'Icon/Clipboard.svg'
                  "
                  width="18px"
                  height="18px"
                />
              </button>
            </div>
            <div
              class="cursor-ponter rounded-lg"
              *ngIf="
                (!message.liked && !message.disliked) ||
                (!message.disliked && message.liked)
              "
            >
              <button
                class="btn btn-sm px-1"
                (click)="handleUserFeedback(i, 'like'); getMsgID()"
              >
                <img
                  [src]="
                    messages[i].liked
                      ? assetBasePath + 'Icon/ThumbsUp-filled.svg'
                      : assetBasePath + 'Icon/ThumbsUp.svg'
                  "
                  width="18px"
                  height="18px"
                />
              </button>
            </div>
            <div
              class="cursor-ponter rounded-lg"
              *ngIf="
                (!message.liked && !message.disliked) ||
                (message.disliked && !message.liked)
              "
            >
              <button
                class="btn btn-sm px-1"
                (click)="handleUserFeedback(i, 'dislike')"
              >
                <img
                  [src]="
                    messages[i].disliked
                      ? assetBasePath + 'Icon/ThumbsDown-filled.svg'
                      : assetBasePath + 'Icon/ThumbsDown.svg'
                  "
                  width="18px"
                  height="18px"
                />
              </button>
            </div>
            <div
              class="cursor-ponter rounded-lg"
              *ngIf="i == messages.length - 1"
            >
              <button
                class="btn btn-sm px-1"
                (click)="refreshResponse(i, message)"
              >
                <img
                  src="{{ assetBasePath }}Icon/ArrowsClockwise.svg"
                  width="18px"
                  height="18px"
                />
              </button>
            </div>
          </div>
        </div>
        <div *ngIf="i === messages.length - 1 && isrefreshing">
          <div class="d-flex flex-row py-4" *ngIf="isTyping">
            <div class="mr-2">
              <img
                src="{{ assetBasePath }}Logo-O-BOT-Small.png"
                width="24px"
                height="24px"
              />
            </div>
            <div class="loading-indicator">
              <span class="shimmer">Analyzing</span>
              <div class="loader">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="col px-0"
      [ngClass]="
        this.fullScreenToggle == true
          ? 'input-query fullscreen-view'
          : 'input-query'
      "
    >
      <div class="col px-1 bg-white rounded-lg">
        <div class="flex-fill px-2">
          <textarea
            class="form-control border-0"
            type="text"
            rows="2"
            placeholder="Enter your query here"
            [(ngModel)]="userInput"
          ></textarea>
        </div>
        <div class="d-flex flex-row justify-content-between">
          <div class="d-flex flex-row">
            <div
              class="p-2 rounded col cursor-pointer"
              (click)="fileInput.click()"
            >
              <img
                src="{{ assetBasePath }}Icon/chat/clip.svg"
                height="24px"
                width="24px"
              />
              <input
                #fileInput
                type="file"
                accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                (change)="onFileChange($event)"
                style="opacity: 0; position: absolute; z-index: -1"
              />
            </div>
            <!-- Display Attached File -->

            <div
              *ngIf="attachedFileName"
              class="d-flex align-items-center border-primary rounded-lg px-2 position-relative"
            >
              <div
                class="bg-primary rounded-lg align-items-center d-flex justify-content-center"
                style="min-height: 24px; min-width: 24px; max-height: 24px"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  fill="#000000"
                  viewBox="0 0 256 256"
                >
                  <path
                    d="M224,48H32a8,8,0,0,0-8,8V192a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A8,8,0,0,0,224,48ZM40,112H80v32H40Zm56,0H216v32H96ZM216,64V96H40V64ZM40,160H80v32H40Zm176,32H96V160H216v32Z"
                  ></path>
                </svg>
              </div>
              <span class="ml-2 text-muted">{{ attachedFileName }}</span>
              <div
                class="rounded-circle bg-primary position-absolute text-center align-items-center justify-content-center text-white d-flex cursor-pointer"
                (click)="removeAttachment()"
                style="
                  min-width: 20px;
                  max-width: 20px;
                  min-height: 20px;
                  max-height: 20px;
                  top: -10px;
                  right: -10px;
                "
              >
                x
              </div>
            </div>
          </div>

          <div
            class="p-2 rounded-lg bg-primary m-1 cursor-pointer"
            (click)="sendMessage()"
            *ngIf="!isTyping && userInput != ''"
          >
            <img src="{{ assetBasePath }}Icon/chat/arrow-up.svg" />
          </div>
          <div
            class="p-2 rounded-lg bg-primary-500 m-1"
            *ngIf="isTyping || userInput == ''"
          >
            <img src="{{ assetBasePath }}Icon/chat/arrow-up.svg" />
          </div>
        </div>
      </div>

      <!-- <div class="d-flex flex-row align-items-end bg-white rounded-lg">
      <div class="p-2 rounded col">
        <img
          src="{{ assetBasePath }}Icon/chat/clip.svg"
          height="24px"
          width="24px"
        />
      </div>
      <div class="flex-fill px-2 col">
        <textarea
          class="form-control border-0"
          type="text"
          rows="2"
          placeholder="Enter your query here"
          [(ngModel)]="userInput"
        ></textarea>
      </div>
      <div class="p-2 rounded-lg bg-primary m-1 col" (click)="sendMessage()">
        <img src="{{ assetBasePath }}Icon/chat/arrow-up.svg" />
      </div>
    </div> -->
    </div>
  </div>
</div>
<!-- Modal Active chat -->
<div
  class="modal fade"
  id="openModalDoc"
  tabindex="-1"
  role="dialog"
  aria-labelledby="openModalDocLabel"
  aria-hidden="true"
>
  <div class="modal-dialog" role="document" *ngFor="let message of messages">
    <div
      class="modal-content"
      *ngIf="
        message.sender === 'bot' && message.tableData && message.tableVisible
      "
    >
      <div class="modal-header bg-primary">
        <h5 class="modal-title text-white" id="openModalDocLabel">
          FMEA for Copper Plated Ceramic Sparkplugs
        </h5>
        <button
          type="button"
          class="close text-white"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div *ngIf="message.sender === 'bot'" class="d-flex flex-row">
          <div class="mr-2">
            <img
              src="{{ assetBasePath }}Logo-O-BOT-Small.png"
              width="24px"
              height="24px"
            />
          </div>
          <div>
            <div class="shadow1 flex-fill">
              <div class="p-2">
                <!-- Display table if it exists and is visible -->
                <table
                  *ngIf="message.tableData && message.tableVisible"
                  class="table table-striped mt-2"
                >
                  <thead>
                    <tr>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="let row of message.tableData"
                      [ngClass]="{
                        'fade-in': row.visible,
                        'd-none': !row.visible
                      }"
                    >
                      <td></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="openModalConfirm"
  tabindex="-1"
  role="dialog"
  aria-labelledby="openModalConfirmLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-primary">
        <h5 class="modal-title text-white" id="openModalConfirmLabel">
          Send To AquaPro
        </h5>
        <button
          type="button"
          class="close text-white"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="d-flex flex-column text-center">
          <div class="mb-2">
            <img src="{{ assetBasePath }}Icon/FileArrowUp.png" />
          </div>
          <div><h4>Are You Sure!</h4></div>

          <p>Do you want to send this generated record to AquaPro?</p>
          <div class="d-flex flex-row flex-fill justify-content-center">
            <div class>
              <button
                class="btn btn-outline-primary mr-2 flex-fill"
                (click)="cancelAquaProSend()"
                data-dismiss="modal"
              >
                <img src="{{ assetBasePath }}Icon/X-primary.svg" class="mr-2" />
                NO
              </button>
            </div>
            <div>
              <button
                class="btn btn-primary mr-2"
                (click)="sendToAquaPro()"
                [disabled]="isProcessingAquaPro"
              >
                <img
                  src="{{ assetBasePath }}Icon/Check-white.svg"
                  class="mr-2"
                />
                <span *ngIf="!isProcessingAquaPro">YES</span>
                <span *ngIf="isProcessingAquaPro">Processing...</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
