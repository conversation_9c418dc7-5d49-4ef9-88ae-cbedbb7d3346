import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { Observable, map } from 'rxjs';
import { AlertService } from 'src/app/services/alert/alert.service';
import { ChatService } from 'src/app/services/chat/chat.service';
import { DateService } from 'src/app/services/date/date.service';
import { LogService } from 'src/app/services/log/log.service';
import { PageSelectionService } from 'src/app/services/page-select/page-selection.service';
import { environment } from 'src/environments/environment.development';

@Component({
  selector: 'app-pinned-items',
  templateUrl: './pinned-items.component.html',
  styleUrls: ['./pinned-items.component.css'],
})
export class PinnedItemsComponent {
  externalText = '';
  entries: any = [];

  url: string = environment.apiUrl;

  private apiUrl = `${this.url}/chat/pinned`;

  totalCount: number = 0;
  currentPage: number = 1;
  itemsPerPage = 10;

  groupedEntries: { [date: string]: any[] } = {};

  isLoading: boolean = true;

  assetBasePath: string = environment.assetBasePath;

  constructor(
    private pageSelectionService: PageSelectionService,
    private chatService: ChatService,
    private alertService: AlertService,
    private http: HttpClient,
    private dateService: DateService,
    private logService: LogService
  ) {}

  ngOnInit(): void {
    this.getPinnedItem();
  }

  getPinnedItem(): void {
    const skip = (this.currentPage - 1) * this.itemsPerPage;
    const limit = this.itemsPerPage;
    const apiUrl = `${this.apiUrl}?skip=${skip}&limit=${limit}`;
    this.logService.log('Fetching prompts from:' + apiUrl); // Debugging API call
    this.isLoading = true;
    this.http.get<any>(apiUrl, { withCredentials: true }).subscribe(
      (response) => {
        if (response && response.pinned_chats) {
          this.logService.log(response);
          this.entries = response.pinned_chats;
          this.totalCount = response.total_count;
          this.groupEntriesByDate();
        }
        this.isLoading = false;
      },
      (error) => {
        this.logService.error('Error fetching prompts:' + error);
        this.alertService.showAlert(
          'Failed to load prompts. Please try again later.',
          'error'
        );
      }
    );
  }

  unPinEntry(index: number): void {
    const entry = this.entries[index]; // Get the entry to unpin
    const apiUnpinUrl = `${this.apiUrl}/${entry.chat_id}/pin?attempt=1`;

    const requestBody = {
      message: 'Pin status updated successfully',
      chat_id: entry.chat_id,
      pinned: false,
    };

    // Call API to unpin the entry
    this.http
      .put(apiUnpinUrl, requestBody, { withCredentials: true })
      .subscribe(
        (response: any) => {
          this.logService.log('Unpin successful:' + response);
          this.alertService.showAlert('Item successfully unpinned!', 'success');

          // Remove the entry from the pinned list
          this.entries.splice(index, 1);

          // Recalculate pagination and regroup entries
          this.totalCount--;
          this.groupEntriesByDate();
        },
        (error) => {
          this.logService.error('Error unpinning the entry:' + error);
          this.alertService.showAlert(
            'Failed to unpin the item. Please try again.',
            'error'
          );
        }
      );
  }

  toChatPage(page: string) {
    this.pageSelectionService.setSelectedPage(page);
    if (page === 'New Chat') {
      this.chatService.clearChatState(); // Clear state when moving to New Chat
    }
  }

  startChat(query: string) {
    this.chatService.getRecentChat(query);
    this.chatService.getPreviusChat(query).then(() => {
      this.pageSelectionService.setSelectedPage('New Chat');
    });
  }

  clearExternalInput() {
    this.externalText = ''; // Clear the external input
    this.chatService.clearExternalInput(); // Clear from ChatService
    this.chatService.clearChatState(); // Reset the chat state
    // Optionally, reset any other state or UI related to the chat
  }

  // Group entries by their respective dates
  groupEntriesByDate(): void {
    this.groupedEntries = {}; // Clear previous groupings
    this.entries.forEach((entry: any) => {
      const date = this.dateService.formatDate(entry.created_at); // Format entry date
      if (!this.groupedEntries[date]) {
        this.groupedEntries[date] = [];
      }
      this.groupedEntries[date].push(entry);
    });
  }
  getKeys(obj: any): string[] {
    return Object.keys(obj);
  }

  onPageChange(newPage: number) {
    this.currentPage = newPage;
    this.getPinnedItem();
  }
}
