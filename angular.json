{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"o_bot": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/o_bot", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "EwIMSNew/Scripts/client/assets", "output": "EwQIMS_Inst1/common/EwIMSNew/Scripts/client/assets"}], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/popper.js/dist/umd/popper.min.js", "node_modules/bootstrap/dist/js/bootstrap.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1.35mb", "maximumError": "1.5mb"}, {"type": "anyComponentStyle", "maximumWarning": "5kb", "maximumError": "10kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.development.ts", "with": "src/environments/environment.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"options": {"proxyConfig": "src/proxy.conf.json"}, "builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "o_bot:build:production"}, "development": {"browserTarget": "o_bot:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "o_bot:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "src/styles.css"], "scripts": []}}}}}}