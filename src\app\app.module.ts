import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule } from '@angular/forms';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { OBotComponent } from './pages/o-bot/o-bot.component';
import { NotificationsComponent } from './pages/o-bot/notifications/notifications.component';
import { PromptVaultComponent } from './pages/o-bot/prompt-vault/prompt-vault.component';
import { PinnedItemsComponent } from './pages/o-bot/pinned-items/pinned-items.component';
import { ChatComponent } from './pages/o-bot/chat/chat.component';
import { RecentChatComponent } from './pages/o-bot/recent-chat/recent-chat.component';
import { HelpSupportComponent } from './pages/o-bot/help-support/help-support.component';
import { UserSettingsComponent } from './pages/o-bot/user-settings/user-settings.component';
import { ActiveSupportComponent } from './pages/o-bot/help-support/active-support/active-support.component';
import { AlertComponent } from './component/alert/alert.component';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { PaginationComponent } from './component/pagination/pagination/pagination.component';
import { AuthInterceptor } from './interceptors/auth/auth.interceptor';

@NgModule({
  declarations: [
    AppComponent,
    OBotComponent,
    NotificationsComponent,
    PromptVaultComponent,
    PinnedItemsComponent,
    ChatComponent,
    RecentChatComponent,
    HelpSupportComponent,
    UserSettingsComponent,
    ActiveSupportComponent,
    AlertComponent,
    PaginationComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    NgSelectModule,
    FormsModule,
    HttpClientModule,
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
