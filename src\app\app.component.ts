import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { PageSelectionService } from './services/page-select/page-selection.service';
import { ChatService } from './services/chat/chat.service';
import { Subscription } from 'rxjs';
import { ThemeSwitchService } from './services/theme/theme-switch.service';
import { LogService } from './services/log/log.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
})
export class AppComponent {
  theme: 'light' | 'dark';
  title = 'o_bot';
  isOmnexWindow: boolean = false;
  isObot: boolean = false;
  externalText = '';
  selectedPage: string = '';
  public href: string = '';
  private pageSelectionSubscription!: Subscription;

  constructor(
    private router: Router,
    private pageSelectionService: PageSelectionService,
    private chatService: ChatService,
    private themeService: ThemeSwitchService,
    private logService: LogService
  ) {
    this.href = this.router.url;
    this.logService.log('url current' + this.href);
    if (this.href == '/') {
      this.isOmnexWindow = true;
      this.isObot = false;
    } else if (this.href == 'o-bot') {
      this.isOmnexWindow = false;
      this.isObot = true;
      this.logService.log('url current' + this.href);
    }
    this.theme = this.themeService.getCurrentTheme();
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.theme = this.themeService.getCurrentTheme();
  }

  toChat(page: string) {
    this.pageSelectionService.setSelectedPage(page);
  }
  onFullscreenToggled(isToggled: boolean) {
    console.log('Fullscreen mode is now:', isToggled ? 'Enabled' : 'Disabled');
  }

  toChatComponent(text: string) {
    this.chatService.saveExternalInput(text);
    this.logService.log('' + this.chatService.saveExternalInput(text));
  }

  clearExternalInput() {
    this.externalText = ''; // Clear the external input
    this.chatService.clearExternalInput(); // Clear from ChatService
    this.chatService.clearChatState(); // Reset the chat state
    // Optionally, reset any other state or UI related to the chat
  }

  clearLocalStorage() {
    localStorage.removeItem('selectedPage');
    this.selectedPage = 'New Chat';
  }

  ngOnInit() {
    this.pageSelectionSubscription =
      this.pageSelectionService.selectedPage$.subscribe((page) => {
        this.selectedPage = page;
        this.logService.log('Selected Page:' + this.selectedPage);
      });
  }
  ngOnDestroy() {
    // Unsubscribe to avoid memory leaks
    if (this.pageSelectionSubscription) {
      this.pageSelectionSubscription.unsubscribe();
    }
  }
}
