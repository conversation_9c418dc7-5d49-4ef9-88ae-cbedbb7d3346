import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment.development';

@Injectable({
  providedIn: 'root',
})
export class LogService {
  constructor() {}

  log(message: string): void {
    if (environment.production) {
      // Log disabled in production
      return;
    }
    console.log(message); // Enable logs in non-production environments
  }

  warn(message: string): void {
    if (environment.production) {
      return;
    }
    console.warn(message); // Enable warning logs in non-production environments
  }

  error(message: string): void {
    if (environment.production) {
      return;
    }
    console.error(message); // Enable error logs in non-production environments
  }
}
