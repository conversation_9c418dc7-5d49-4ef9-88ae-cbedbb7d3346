import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from 'src/environments/environment.development';
import { LogService } from '../log/log.service';
import { AquaProResponse } from 'src/app/interfaces/aquapro-response.interface';

@Injectable({
  providedIn: 'root',
})
export class ChatService {
  private chatStateKey = 'chatState'; // Key for storing chat state in local storage
  private externalInputKey = 'externalInput'; // Key for storing external input
  private convIdKey = 'convId'; // Key for storing external input
  private chatTitleKey = 'chatTitle';

  url: string = environment.apiUrl;
  private apiUrl = `${this.url}/chat/active2?attempt=1`;
  private baseUrl = `${this.url}/chat`;

  private chatTitleSubject = new BehaviorSubject<string>('New Chat'); // Default title
  chatTitle$ = this.chatTitleSubject.asObservable();

  constructor(private http: HttpClient, private logService: LogService) {}

  clearChatState() {
    localStorage.removeItem(this.chatStateKey);
    localStorage.removeItem(this.externalInputKey);
    localStorage.removeItem(this.convIdKey);
    this.chatTitleSubject.next('New Chat');
    localStorage.removeItem(this.chatTitleKey);
  }

  getChatState() {
    return JSON.parse(localStorage.getItem('chatState') || 'null'); // Load the saved state
  }

  saveChatState(state: any) {
    localStorage.setItem('chatState', JSON.stringify(state)); // Save the state
    localStorage.setItem('chatStateHistory', JSON.stringify(state));
  }

  // Get the external input if available
  getExternalInput(): string | null {
    return localStorage.getItem(this.externalInputKey);
  }

  // Get the Recent Chat input if available
  getRecentInput(): string | null {
    return localStorage.getItem(this.chatStateKey);
  }

  getRecentChat(key: any) {
    localStorage.removeItem(this.convIdKey);
    this.logService.log(key);
    this.getPreviusChat(key);
  }
  getPreviusChat(chat_id: any): Promise<void> {
    return new Promise((resolve, reject) => {
      const apiUrl = `${this.baseUrl}/${chat_id}`;
      this.logService.log('Fetching prompts from:' + apiUrl); // Debugging API call

      this.http.get<any>(apiUrl, { withCredentials: true }).subscribe(
        (response) => {
          if (response && response.messages) {
            const details = {
              chat_id: response.chat_id,
              title: response.title,
              created_at: response.created_at,
            };
            const newTitle = details.title;
            this.chatTitleSubject.next(newTitle);
            this.setChatTitle(newTitle);
            const messages = response.messages;
            // Transform messages to the desired format
            const transformedMessages = messages.map((msg: any) => ({
              text: msg.message_text,
              sender: msg.sender === 'assistant' ? 'bot' : msg.sender,
              convID: msg.chat_id,
              messageID: msg.message_id,
              extraData:
                msg.extra_data &&
                typeof msg.extra_data === 'object' &&
                !Array.isArray(msg.extra_data)
                  ? Object.keys(msg.extra_data).length > 0
                    ? msg.extra_data
                    : null
                  : null,
            }));

            // Store the transformed messages in localStorage

            localStorage.setItem('convId', details.chat_id);
            localStorage.setItem(
              'chatStateHistory',
              JSON.stringify({
                isChatActive: true,
                messages: transformedMessages,
                userInput: '',
              })
            );
            localStorage.setItem(
              'chatState',
              JSON.stringify({
                isChatActive: true,
                messages: transformedMessages,
                userInput: '',
              })
            );

            this.logService.log(
              'Stored Messages in LocalStorage:' + transformedMessages
            );
            resolve();
          } else {
            this.logService.error('Unexpected API response format:' + response);
            reject();
          }
        },
        (error) => {
          this.logService.error('Error fetching prompts:' + error);
          reject();
          // this.alertService.showAlert(
          //   'Failed to load prompts. Please try again later.',
          //   '#FF5733'
          // );
        }
      );
    });
  }
  // Save external input
  saveExternalInput(input: string) {
    localStorage.setItem(this.externalInputKey, input);
    localStorage.removeItem(this.convIdKey);
  }

  // Clear external input
  clearExternalInput() {
    localStorage.removeItem(this.externalInputKey);
    localStorage.removeItem(this.convIdKey);
  }

  // Method to send user input to the API
  sendUserQuestion(
    userQuestion: string,
    conveId?: string,
    file?: File | null
  ): Observable<any> {
    console.log('user_file', file);
    const convId = localStorage.getItem('convId');
    const formData = new FormData();
    formData.append('user_input', userQuestion);
    formData.append('conversation_id', convId || '');
    if (file) {
      formData.append('document', file, file.name);
    } else {
      formData.append('document', '');
    }

    const headers = new HttpHeaders().set('accept', 'application/json');

    return this.http.post(this.apiUrl, formData, { headers });
  }

  setChatTitle(newTitle: string): void {
    this.chatTitleSubject.next(newTitle);
    localStorage.setItem(this.chatTitleKey, newTitle);
  }

  getChatTitle(): string {
    return this.chatTitleSubject.getValue();
  }
  loadChatTitle() {
    const savedTitle = localStorage.getItem(this.chatTitleKey);
    if (savedTitle) {
      this.chatTitleSubject.next(savedTitle);
    }
  }

  /**
   * Creates AquaPro data using the AquaPro Integration API
   * @param messageId The message ID to process
   * @param attempt The attempt number (default: 1)
   * @returns Observable with the API response
   */
  createAquaProData(
    messageId: string,
    attempt: number = 1
  ): Observable<AquaProResponse> {
    const apiUrl = `${this.url}/aquapro/`;

    // Set up query parameters
    const params = new HttpParams()
      .set('message_id', messageId)
      .set('attempt', attempt.toString());

    // Set up headers
    const headers = new HttpHeaders().set('accept', 'application/json');

    // Make the POST request
    this.logService.log(`Creating AquaPro data for message ID: ${messageId}`);
    return this.http.post<AquaProResponse>(apiUrl, null, {
      headers,
      params,
      withCredentials: true,
    });
  }
}
