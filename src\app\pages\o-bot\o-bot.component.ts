import {
  Component,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { ChatService } from 'src/app/services/chat/chat.service';
import { Router } from '@angular/router';
import { ChatComponent } from './chat/chat.component';
import { PageSelectionService } from 'src/app/services/page-select/page-selection.service';
import { Subscription } from 'rxjs';
import { environment } from 'src/environments/environment.development';
import { LogService } from 'src/app/services/log/log.service';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert/alert.service';
import { UserInfoService } from 'src/app/services/user-info/user-info.service';
import { SessionTokenService } from 'src/app/services/sessionToken/session-token.service';
interface Link {
  id: number;
  pageName: string;
  isActive: boolean;
  filledIcon: string;
  regularIcon: string;
}

@Component({
  selector: 'app-o-bot',
  templateUrl: './o-bot.component.html',
  styleUrls: ['./o-bot.component.css'],
})
export class OBotComponent {
  @ViewChild('chatComp') chatComp!: ChatComponent;
  @Output() fullscreenToggled = new EventEmitter<boolean>();
  isOmnexWindow: boolean = false;
  isObot: boolean = false;
  obotWindowToggle: boolean = true;
  selectedPage: string = '';
  sideBarOpen: boolean = false;
  fullScreenToggle: boolean = false;
  apiUrl: string = environment.apiUrl;
  loading: boolean = true; // For loading screen display

  hoveredLinkId: number | null = null;
  externalText = '';

  name: string = 'John Doe';
  email: string = '<EMAIL>';
  initials: string = '';
  avatarColor: string = '';

  testChatName: string = '';

  private pageSelectionSubscription: Subscription | null = null;
  private chatTitleSubscription: Subscription | null = null;
  buttonNewChat: boolean = false;

  url: string = environment.apiUrl;
  private pinUrl = `${this.url}/chat`;
  convID: string | null = null;

  assetBasePath: string = environment.assetBasePath;

  constructor(
    private chatService: ChatService,
    private pageSelectionService: PageSelectionService,
    private logService: LogService,
    private http: HttpClient,
    private alertService: AlertService,
    private userService: UserInfoService,
    private sessionTokenService: SessionTokenService
  ) {}

  setting: Link = {
    id: 0,
    pageName: 'User Settings',
    isActive: false,
    filledIcon: `${this.assetBasePath}Icon/Wrench-Fill.svg`,
    regularIcon: `${this.assetBasePath}Icon/Wrench.svg`,
  };
  pinned: Link = {
    id: 0,
    pageName: 'Pinned Items',
    isActive: false,
    filledIcon: `${this.assetBasePath}Icon/PushPin-Fill.svg`,
    regularIcon: `${this.assetBasePath}Icon/PushPin.svg`,
  };

  links: Link[] = [
    {
      id: 1,
      pageName: 'Notifications',
      isActive: false,
      filledIcon: `${this.assetBasePath}Icon/Bell-Fill.svg`,
      regularIcon: `${this.assetBasePath}Icon/Bell.svg`,
    },
    {
      id: 2,
      pageName: 'Prompt Vault',
      isActive: false,
      filledIcon: `${this.assetBasePath}Icon/Vault-Fill.svg`,
      regularIcon: `${this.assetBasePath}Icon/Vault.svg`,
    },
    {
      id: 3,
      pageName: 'Pinned Items',
      isActive: false,
      filledIcon: `${this.assetBasePath}Icon/PushPin-Fill.svg`,
      regularIcon: `${this.assetBasePath}Icon/PushPin.svg`,
    },
    {
      id: 4,
      pageName: 'Recent Chats',
      isActive: false,
      filledIcon: `${this.assetBasePath}Icon/ClockCounterClockwise.svg`,
      regularIcon: `${this.assetBasePath}Icon/ClockCounterClockwise.svg`,
    },
    {
      id: 5,
      pageName: 'Support',
      isActive: false,
      filledIcon: `${this.assetBasePath}Icon/Question-Fill.svg`,
      regularIcon: `${this.assetBasePath}Icon/Question.svg`,
    },
  ];
  userName: string = '';
  userPhoto: string | null = '';

  isFullscreen = false;
  ngOnInit() {
    this.setNewChat();
    this.sessionTokenService.checkForTokenChange();
    this.fetchUserInfo();

    this.convID = localStorage.getItem('convID');
    this.chatService.loadChatTitle();
    this.pageSelectionSubscription =
      this.pageSelectionService.selectedPage$.subscribe((page) => {
        this.selectedPage = page;
        this.updateActiveLink(page);
      });
    this.chatTitleSubscription = this.chatService.chatTitle$.subscribe(
      (newTitle) => {
        this.testChatName = newTitle;
      }
    );
    const savedPage = localStorage.getItem('selectedPage');
    this.selectedPage = savedPage ? savedPage : 'New Chat'; // Default page
    this.simulateLoading();

    this.avatarColor = this.getRandomColor();

    console.log(
      '🔹 Listening for messages from parent window',
      this.selectedPage
    );

    // Listen for messages from parent (.NET)
    window.addEventListener('message', this.handleMessage, false);
  }

  fetchUserInfo(): void {
    this.userService.getUserInfo().subscribe(
      (data) => {
        this.userName = data.userName || 'Unknown User'; // Fallback for userName
        this.userPhoto = data.userPhoto || null; // Handle null photo gracefully
        this.initials = this.getInitials(this.userName);

        console.log(
          'Photo:',
          this.userPhoto ? 'Photo loaded' : 'No photo available'
        );
      },
      (error) => {
        console.error('Error fetching user info:', error);
        this.userName = 'Unknown User'; // Fallback on error
        this.userPhoto = null; // No photo on error
        this.initials = this.getInitials(this.userName);
      }
    );
  }
  ngOnDestroy() {
    if (this.chatTitleSubscription) {
      this.chatTitleSubscription.unsubscribe();
    }
    // Remove listener to prevent memory leaks
    window.removeEventListener('message', this.handleMessage);
  }

  handleMessage = (event: MessageEvent): void => {
    // Ensure the message is of type "fullscreenToggled"
    if (!event.data || event.data.type !== 'fullscreenToggled') return;

    // Update fullscreen state
    this.isFullscreen = event.data.isToggled;
    this.fullScreenToggle = !event.data.isToggled;
    console.log(
      '✅ Fullscreen State Updated from Parent:',
      this.isFullscreen ? 'ON' : 'OFF'
    );
  };

  simulateLoading() {
    this.pageSelectionService.selectedPage$.subscribe((page) => {
      this.selectedPage = page;
    });

    this.loading = true; // Set loading to true
    setTimeout(() => {
      this.loading = false; // Stop loading after 3 seconds
    }, 3000);
  }

  ObotWindow() {
    this.obotWindowToggle = !this.obotWindowToggle;
    this.logService.log('obotWindowToggle:' + this.obotWindowToggle);
  }

  sideBar() {
    this.sideBarOpen = !this.sideBarOpen;
  }
  fullScreenView() {
    this.fullScreenToggle = !this.fullScreenToggle;
  }

  getInitials(name: string): string {
    const names = name.split(' ');
    const initials = names.map((n) => n.charAt(0)).join('');
    return initials.length > 2 ? initials.slice(0, 2) : initials; // Limit to 2 characters
  }

  getRandomColor(): string {
    const colors = ['#ff5733', '#33c3ff', '#28a745', '#ffc107', '#6f42c1'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  closeAgent() {
    this.fullScreenToggle = false;
  }

  updateActiveLink(page: string) {
    this.links.forEach((link) => (link.isActive = link.pageName === page));
    this.setting.isActive = this.setting.pageName === page;
  }
  randomNumber: number | null = null;

  generateRandomNumber(): void {
    // Generate a random number between 123 and 999
    this.randomNumber = Math.floor(Math.random() * (999 - 123 + 1)) + 123;
  }

  selectPage(page: string, chatComp?: ChatComponent) {
    this.pageSelectionService.setSelectedPage(page);
    this.logService.log('hii out  ' + page);
    if (page === 'New Chat') {
      if (page === 'New Chat' && chatComp) {
        this.chatService.clearChatState(); // Clear chat state using the service
        chatComp.clearChat(); // Reset the chat component
        //
      } else {
        this.chatService.clearChatState();
      }
    }
    if (page != 'New Chat') {
      this.buttonNewChat = false;
    }

    this.updateActiveLink(page);

    this.links.forEach((link) => (link.isActive = link.pageName === page));
    this.setting.isActive = this.setting.pageName === page;
    this.selectedPage = page;
    if (page === this.setting.pageName) {
      this.setting.isActive = true;
    } else {
      const activeLink = this.links.find((link) => link.pageName === page);
      if (activeLink) activeLink.isActive = true;
    }

    this.loading = false;
    localStorage.setItem('selectedPage', page);
    this.sideBarOpen = false; // Optionally close the sidebar after selection
  }

  clearLocalStorage() {
    localStorage.removeItem('selectedPage');
    this.selectedPage = 'New Chat';
    this.simulateLoading();
  }

  toChatComponent(text: string) {
    this.externalText = text;
    this.chatService.saveExternalInput(text);
  }
  setNewChat() {
    return (this.buttonNewChat = true);
  }

  pinChat(): void {
    const chatId: string | null = localStorage.getItem('convId');
    const apiUrl = `${this.pinUrl}/pinned/${chatId}/pin?attempt=0`; // Construct API URL
    this.http.put(apiUrl, { withCredentials: true }).subscribe(
      (response: any) => {
        const pinned = response.pinned; // Extract pinned status from the response
        this.pinned.isActive = pinned;
        const message = pinned
          ? 'Chat pinned successfully!'
          : 'Chat unpinned successfully!';
        this.alertService.showAlert(message, pinned ? 'success' : 'warning'); // Show alert based on status
        console.log('Response:', response);
      },
      (error) => {
        console.error('Error updating pin status:', error);
        this.alertService.showAlert(
          'Failed to update pin status. Please try again.',
          'error'
        ); // Show error message
      }
    );
  }
}
