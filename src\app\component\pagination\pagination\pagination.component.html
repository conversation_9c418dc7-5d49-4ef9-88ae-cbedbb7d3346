<!-- Pagination Controls -->
<div class="mb-5" *ngIf="totalPages > 1">
  <div class="pagination-container justify-content-end">
    <!-- Navigation Controls -->
    <button
      class="btn btn-sm btn-warning"
      (click)="goToFirstPage()"
      [disabled]="currentPage === 1"
    >
      «
    </button>
    <button
      class="btn btn-sm btn-warning"
      (click)="goToPreviousPage()"
      [disabled]="currentPage === 1"
    >
      ‹
    </button>

    <!-- Page Numbers -->
    <span
      *ngFor="let page of visiblePages"
      class="pagination-number"
      [class.active]="page === currentPage"
      (click)="goToPage(page)"
    >
      {{ page }}
    </span>

    <!-- Navigation Controls -->
    <button
      class="btn btn-sm btn-warning"
      (click)="goToNextPage()"
      [disabled]="currentPage === totalPages"
    >
      ›
    </button>
    <button
      class="btn btn-sm btn-warning"
      (click)="goToLastPage()"
      [disabled]="currentPage === totalPages"
    >
      »
    </button>
  </div>
  <!-- Additional Details -->
  <div class="pagination-info justify-content-between mt-2">
    <span>
      {{ (currentPage - 1) * itemsPerPage + 1 }}–{{
        Math.min(currentPage * itemsPerPage, totalCount)
      }}
      of {{ totalCount }}
    </span>
    <div class="d-flex flex-nowrap align-items-center">
      Jump To
      <input
        class="form-control form-control-sm ml-1"
        type="number"
        [(ngModel)]="jumpToPage"
        (keyup.enter)="goToSpecificPage()"
        placeholder="Page"
        [min]="1"
        [max]="totalPages"
      />
    </div>
  </div>
</div>
